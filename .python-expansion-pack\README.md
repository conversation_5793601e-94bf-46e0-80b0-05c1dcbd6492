# Python科学计算扩展包

专门用于Python科学计算、数据可视化、神经网络和数字信号处理的BMAD扩展包。

## 🎯 认识您的Python科学计算专家团队

### 🧬 Dr. Alex Chen (陈博士) - Python科学计算项目协调员

*拥有计算机科学博士学位，15年Python科学计算经验，专精数据科学、机器学习和信号处理*

Dr. Alex Chen 是您的Python科学计算项目协调员，将通过编号选项和结构化工作流指导您完成整个科学计算开发过程。他擅长项目架构设计、技术选型和最佳实践指导。

### 💼 专业代理团队

- **📊 Sarah Martinez** - 数据可视化专家
  - *数据科学硕士，8年可视化设计经验*
  - 专精: matplotlib, seaborn, plotly, 交互式可视化
  - 风格: 创意丰富，注重美观和可读性

- **🧠 Dr. <PERSON> (张博士)** - 神经网络架构师
  - *机器学习博士，10年深度学习经验*
  - 专精: TensorFlow, PyTorch, 模型设计, 超参数优化
  - 风格: 严谨，系统性思维，注重性能

- **📡 Dr. <PERSON> (罗德里格斯博士)** - 数字信号处理专家
  - *电子工程博士，12年信号处理经验*
  - 专精: scipy.signal, 滤波器设计, 频域分析, 降噪技术
  - 风格: 精确，理论扎实，实用主义

## 🚀 快速开始

### 1. 准备数据文件 (放在 `bmad-core/data/`)

#### `plot-preferences.yml` - 可视化偏好设置
```yaml
style: seaborn-whitegrid
color_palette: husl
figure_size: [10, 6]
dpi: 300
font_size: 12
title_size: 14
grid_alpha: 0.3
```

#### `model-requirements.yml` - 模型需求规格
```yaml
accuracy_threshold: 0.95
max_training_time: 3600  # 秒
max_model_size: 100      # MB
hardware_constraints:
  gpu_memory: 8          # GB
  cpu_cores: 4
validation_split: 0.2
```

#### `signal-specifications.yml` - 信号处理规格
```yaml
sampling_rate: 1000      # Hz
frequency_range: [1, 50] # Hz
noise_level: 0.1
filter_type: bandpass
quality_metrics:
  snr_improvement: 10    # dB
  processing_delay: 100  # ms
```

### 2. 启动项目协调员

```bash
npm run agent python-scientific-orchestrator
```

### 3. 跟随编号选项指导

Dr. Alex Chen 将为您提供编号选择，引导您完成：
- 🚀 项目初始化和环境配置
- 📊 数据可视化项目开发
- 🧠 神经网络模型设计
- 📡 信号处理算法实现
- 📝 技术文档生成
- ✅ 质量检查和验证

### 4. 享受专业开发体验

每个专家都会通过编号选项与您互动，确保专业标准和最佳实践。

## 🎨 高级功能

### 🤖 智能模板系统
- **LLM指令嵌入**: 模板包含智能指导，逐步引导文档生成
- **条件内容**: 根据项目类型自动调整模板内容
- **动态变量**: 自动替换项目特定信息

### 🔄 工作流编排
- **决策树**: 根据项目类型自动选择最佳工作流
- **交接协议**: 专家间无缝协作和信息传递
- **质量门控**: 每个阶段都有质量检查点

### ⭐ 多级质量保证
- **基础验证**: 完整性和基本质量检查
- **全面评估**: 详细的质量和准确性验证
- **专家评估**: 领域特定的高级评估标准

## 📋 支持的项目类型

### 📊 数据可视化项目
- 科学数据图表设计
- 交互式可视化开发
- 统计图表和多维可视化
- 可视化报告生成

### 🧠 神经网络项目
- 模型架构设计和选择
- 超参数优化策略
- 性能评估和模型分析
- 部署准备和优化

### 📡 信号处理项目
- 数字滤波器设计
- 信号分析和特征提取
- 降噪和信号增强
- 频域分析和变换

### 🔬 综合科学计算项目
- 多领域专家协作
- 端到端项目开发
- 系统集成和测试
- 完整文档和部署

## 🛠️ 组件概览

### 代理 (4个)
- 1个项目协调员 + 3个领域专家
- 每个代理都有独特的角色人设和专业背景
- 编号选项协议确保清晰的交互

### 模板 (4个)
- 数据可视化报告模板
- 神经网络架构文档模板
- 信号分析报告模板
- Python项目结构模板

### 质量系统
- 4个专业检查清单
- 星级评分系统
- 准备就绪/未准备就绪判定
- 具体改进建议

### 知识库
- Python科学计算最佳实践
- 数据可视化设计指南
- 神经网络设计模式
- 信号处理算法参考

## 📚 技术栈

### 核心库
- **NumPy**: 数值计算基础
- **Pandas**: 数据处理和分析
- **Matplotlib**: 基础可视化
- **SciPy**: 科学计算工具

### 可视化
- **Seaborn**: 统计可视化
- **Plotly**: 交互式图表
- **Bokeh**: Web可视化

### 机器学习
- **Scikit-learn**: 传统机器学习
- **TensorFlow**: 深度学习框架
- **PyTorch**: 研究导向深度学习

### 信号处理
- **Librosa**: 音频信号处理
- **PyWavelets**: 小波变换

## 🎯 使用场景

### 学术研究
- 数据分析和可视化
- 机器学习模型开发
- 信号处理算法研究
- 科学论文图表制作

### 工业应用
- 数据驱动决策支持
- 预测模型开发
- 信号监测和分析
- 自动化报告生成

### 教育培训
- Python科学计算教学
- 最佳实践演示
- 项目结构标准化
- 代码质量培养

## ✅ 质量标准

### 代码质量
- PEP 8 编码规范
- 完整的文档字符串
- 单元测试覆盖
- 类型提示支持

### 科学严谨性
- 可重现的结果
- 统计方法正确性
- 数据完整性验证
- 误差分析和报告

### 专业标准
- 学术出版要求
- 工业部署标准
- 性能基准测试
- 安全性考虑

## 🔧 自定义和扩展

### 项目规模调整
- **小型项目**: 简化流程，单专家主导
- **中型项目**: 标准工作流，多专家协作
- **大型项目**: 详细里程碑，风险管理

### 领域特定调整
- **学术研究**: 强调可重现性和方法论
- **工业应用**: 重视性能和部署考虑
- **原型开发**: 快速迭代和概念验证

## 📞 获取帮助

### 命令帮助
- 在任何代理中输入 `9` 或 `/help` 获取详细帮助
- 每个代理都提供编号选项指导

### 工作流指导
- 输入 `8` 查看可用工作流
- 协调员会根据项目类型推荐最佳路径

### 质量检查
- 每个阶段都有相应的质量检查清单
- 星级评分系统提供清晰的质量指标

---

**版本**: v1.0.0  
**作者**: BMAD Python科学计算团队  
**最后更新**: 2024年7月

开始您的Python科学计算之旅，让专业的AI代理团队为您提供世界级的技术支持！
