name: python-scientific-team
version: 1.0.0
description: Python科学计算专家团队配置

# 团队组成
agents:
  orchestrator:
    id: python-scientific-orchestrator
    name: Dr. <PERSON> (陈博士)
    role: Python科学计算项目协调员
    icon: 🧬
    primary: true
    description: 资深Python科学计算专家，负责项目协调和技术指导

  specialists:
    - id: data-visualization-expert
      name: <PERSON>
      role: 数据可视化专家
      icon: 📊
      specialties:
        - matplotlib
        - seaborn
        - plotly
        - 交互式可视化
      description: 专精于科学数据可视化和图表设计

    - id: neural-network-architect
      name: Dr. <PERSON> (张博士)
      role: 神经网络架构师
      icon: 🧠
      specialties:
        - TensorFlow
        - PyTorch
        - 模型设计
        - 超参数优化
      description: 深度学习专家，专注于神经网络架构设计

    - id: signal-processing-specialist
      name: Dr. <PERSON> (罗德里格斯博士)
      role: 数字信号处理专家
      icon: 📡
      specialties:
        - scipy.signal
        - 滤波器设计
        - 频域分析
        - 降噪技术
      description: 信号处理专家，精通数字信号分析和处理

# 工作流配置
workflows:
  - python-scientific-workflow

# 支持的项目类型
project_types:
  - visualization: 数据可视化项目
  - neural_network: 神经网络和机器学习项目
  - signal_processing: 数字信号处理项目
  - comprehensive: 综合科学计算项目

# 团队协作模式
collaboration_patterns:
  sequential:
    description: 顺序协作模式
    flow:
      - orchestrator
      - specialist (based on project type)
      - orchestrator (integration)

  parallel:
    description: 并行协作模式
    flow:
      - orchestrator (planning)
      - multiple specialists (parallel work)
      - orchestrator (integration)

  iterative:
    description: 迭代协作模式
    flow:
      - orchestrator
      - specialist
      - review and feedback
      - refinement
      - final integration

# 质量保证
quality_gates:
  - stage: data_preparation
    checklist: python-code-quality-checklist
    required_approval: orchestrator

  - stage: visualization
    checklist: visualization-quality-checklist
    required_approval: data-visualization-expert

  - stage: modeling
    checklist: model-performance-checklist
    required_approval: neural-network-architect

  - stage: signal_processing
    checklist: signal-processing-checklist
    required_approval: signal-processing-specialist

  - stage: final_review
    checklist: python-code-quality-checklist
    required_approval: orchestrator

# 交付物模板
deliverables:
  visualization_project:
    - visualization-report-tmpl
    - python-project-structure-tmpl

  neural_network_project:
    - model-architecture-tmpl
    - python-project-structure-tmpl

  signal_processing_project:
    - signal-analysis-tmpl
    - python-project-structure-tmpl

  comprehensive_project:
    - visualization-report-tmpl
    - model-architecture-tmpl
    - signal-analysis-tmpl
    - python-project-structure-tmpl

# 技能矩阵
skill_matrix:
  data_visualization:
    primary: data-visualization-expert
    secondary: python-scientific-orchestrator
    tools: [matplotlib, seaborn, plotly, bokeh]

  machine_learning:
    primary: neural-network-architect
    secondary: python-scientific-orchestrator
    tools: [tensorflow, pytorch, scikit-learn, keras]

  signal_processing:
    primary: signal-processing-specialist
    secondary: python-scientific-orchestrator
    tools: [scipy, numpy, librosa, pywt]

  data_analysis:
    primary: python-scientific-orchestrator
    secondary: [data-visualization-expert, neural-network-architect]
    tools: [pandas, numpy, scipy, statsmodels]

  project_management:
    primary: python-scientific-orchestrator
    tools: [jupyter, git, conda, pip]

# 通信协议
communication:
  handoff_format:
    - project_context
    - completed_artifacts
    - current_status
    - next_steps
    - quality_requirements

  status_reporting:
    frequency: per_stage
    format: structured_update
    recipients: [orchestrator]

  escalation_path:
    - specialist -> orchestrator
    - orchestrator -> user
    - team_review (if needed)

# 工具和环境
tools:
  required:
    - python: ">=3.8"
    - numpy: ">=1.20.0"
    - pandas: ">=1.3.0"
    - matplotlib: ">=3.4.0"
    - scipy: ">=1.7.0"

  visualization:
    - seaborn: ">=0.11.0"
    - plotly: ">=5.0.0"
    - bokeh: ">=2.3.0"

  machine_learning:
    - scikit-learn: ">=1.0.0"
    - tensorflow: ">=2.6.0"
    - pytorch: ">=1.9.0"

  signal_processing:
    - librosa: ">=0.8.0"
    - pywt: ">=1.1.0"

  development:
    - jupyter: ">=1.0.0"
    - pytest: ">=6.0.0"
    - black: ">=21.0.0"
    - flake8: ">=3.9.0"

# 性能基准
performance_benchmarks:
  visualization:
    render_time: "<5 seconds for standard plots"
    file_size: "<10MB for high-res images"
    memory_usage: "<1GB for large datasets"

  machine_learning:
    training_time: "reasonable for dataset size"
    inference_speed: "real-time for production"
    model_size: "<100MB for deployment"

  signal_processing:
    processing_speed: "real-time for streaming"
    latency: "<100ms for interactive"
    accuracy: ">95% for standard benchmarks"

# 文档标准
documentation:
  code:
    - docstrings for all functions
    - type hints where applicable
    - inline comments for complex logic

  project:
    - README with setup instructions
    - requirements.txt or environment.yml
    - usage examples and tutorials

  deliverables:
    - technical documentation
    - user guides
    - API documentation (if applicable)

# 测试策略
testing:
  unit_tests:
    coverage: ">80%"
    framework: pytest
    scope: individual functions

  integration_tests:
    scope: component interactions
    frequency: per milestone

  performance_tests:
    scope: critical paths
    benchmarks: defined per project type

  validation_tests:
    scope: scientific accuracy
    methods: cross-validation, statistical tests
