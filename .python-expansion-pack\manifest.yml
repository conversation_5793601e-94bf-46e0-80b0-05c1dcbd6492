name: python-scientific-computing
version: 1.0.0
description: >-
  Python科学计算与数据分析扩展包，提供数据可视化、神经网络设计和数字信号处理的专业AI代理支持。
  包含完整的工作流、模板和质量保证系统。
author: BMAD Python科学计算团队
bmad_version: "4.0.0"

# 扩展包中创建的文件
files:
  agents:
    - python-scientific-orchestrator.md  # Dr. <PERSON> Chen - Python科学计算项目协调员
    - data-visualization-expert.md       # <PERSON> Martinez - 数据可视化专家
    - neural-network-architect.md        # Dr. <PERSON> Zhang - 神经网络架构师
    - signal-processing-specialist.md    # Dr. <PERSON> Rodriguez - 信号处理专家

  data:
    - python-scientific-best-practices.md  # Python科学计算最佳实践和标准
    - data-visualization-guidelines.md     # 数据可视化指南和标准
    - neural-network-design-patterns.md    # 神经网络设计模式
    - signal-processing-algorithms.md      # 常用信号处理算法参考

  tasks:
    # 核心工具 (从bmad-core复制)
    - create-doc.md                    # 从模板创建文档
    - execute-checklist.md             # 检查清单验证系统
    # 领域特定任务
    - create-visualization.md          # 创建科学数据可视化
    - optimize-plots.md               # 优化图表性能和美观度
    - design-neural-network.md        # 设计神经网络架构
    - optimize-model.md               # 模型性能优化和调参
    - design-filter.md                # 设计数字滤波器
    - analyze-signal.md               # 信号分析和处理
    - setup-python-environment.md     # 配置Python科学计算环境

  utils:
    # 核心工具 (从bmad-core复制)
    - template-format.md              # 模板标记约定
    - workflow-management.md          # 工作流编排系统

  templates:
    - visualization-report-tmpl.md    # 数据可视化报告模板 (带LLM指令嵌入)
    - model-architecture-tmpl.md     # 神经网络架构文档模板
    - signal-analysis-tmpl.md        # 信号分析报告模板
    - python-project-structure-tmpl.md # Python科学计算项目结构模板

  checklists:
    - visualization-quality-checklist.md  # 数据可视化质量检查 (多级验证)
    - model-performance-checklist.md      # 神经网络模型性能检查
    - signal-processing-checklist.md      # 信号处理质量检查
    - python-code-quality-checklist.md    # Python代码质量检查

  workflows:
    - python-scientific-workflow.md   # 主要工作流 (决策树和交接协议)

  agent-teams:
    - python-scientific-team.yml      # 科学计算团队配置

# 用户必须提供的数据文件 (放在bmad-core/data/目录)
required_user_data:
  - filename: plot-preferences.yml
    description: 数据可视化偏好设置，包括默认样式、颜色方案、字体配置
    format: YAML配置文件
    example: |
      style: seaborn-whitegrid
      color_palette: husl
      figure_size: [10, 6]
      dpi: 300
      font_size: 12
    validation: 检查YAML格式和必需字段

  - filename: model-requirements.yml
    description: 神经网络模型需求规格，定义性能要求和约束条件
    format: YAML配置文件
    example: |
      accuracy_threshold: 0.95
      max_training_time: 3600
      max_model_size: 100MB
      hardware_constraints:
        gpu_memory: 8GB
        cpu_cores: 4
    validation: 检查数值范围和硬件规格合理性

  - filename: signal-specifications.yml
    description: 信号处理规格，定义信号特性和处理要求
    format: YAML配置文件
    example: |
      sampling_rate: 1000
      frequency_range: [1, 50]
      noise_level: 0.1
      filter_type: bandpass
      quality_metrics:
        snr_improvement: 10
    validation: 检查信号参数的物理合理性

# 扩展包内嵌的知识库
embedded_knowledge:
  - python-scientific-best-practices.md
  - data-visualization-guidelines.md
  - neural-network-design-patterns.md
  - signal-processing-algorithms.md

# 对核心BMAD组件的依赖
core_dependencies:
  agents:
    - architect        # 用于系统设计
    - developer        # 用于代码实现
    - qa-specialist    # 用于质量保证
  tasks:
    - create-doc
    - execute-checklist
  workflows:
    - greenfield-fullstack  # 新项目开发
    - brownfield-service    # 现有项目增强

# 代理协调模式
agent_coordination:
  orchestrator: python-scientific-orchestrator
  handoff_protocols: true
  numbered_options: true
  quality_integration: comprehensive
  workflow_management: true

# 角色人设和特色
character_profiles:
  python-scientific-orchestrator:
    name: Dr. Alex Chen (陈博士)
    background: 计算机科学博士，15年Python科学计算经验
    style: 专业、技术导向、循序渐进
    specialties: [项目管理, 技术架构, 最佳实践]

  data-visualization-expert:
    name: Sarah Martinez
    background: 数据科学硕士，8年可视化设计经验
    style: 创意丰富、注重美观和可读性
    specialties: [matplotlib, seaborn, plotly, 设计美学]

  neural-network-architect:
    name: Dr. Michael Zhang (张博士)
    background: 机器学习博士，10年深度学习经验
    style: 严谨、系统性思维、注重性能
    specialties: [TensorFlow, PyTorch, 模型优化, 架构设计]

  signal-processing-specialist:
    name: Dr. Emily Rodriguez (罗德里格斯博士)
    background: 电子工程博士，12年信号处理经验
    style: 精确、理论扎实、实用主义
    specialties: [scipy.signal, 滤波器设计, 频域分析, 降噪]

# 技术栈和工具
technology_stack:
  core_libraries:
    - numpy: ">=1.20.0"
    - pandas: ">=1.3.0"
    - matplotlib: ">=3.4.0"
    - scipy: ">=1.7.0"

  visualization:
    - seaborn: ">=0.11.0"
    - plotly: ">=5.0.0"
    - bokeh: ">=2.3.0"

  machine_learning:
    - scikit-learn: ">=1.0.0"
    - tensorflow: ">=2.6.0"
    - pytorch: ">=1.9.0"

  signal_processing:
    - librosa: ">=0.8.0"
    - pywt: ">=1.1.0"

# 质量保证系统
quality_assurance:
  validation_levels:
    - basic: 基础完整性检查
    - comprehensive: 全面质量验证
    - expert: 专家级评估标准

  rating_systems:
    - star_ratings: 1-5星质量评分
    - binary_decisions: 准备就绪/未准备就绪判定
    - improvement_recommendations: 具体改进建议

  checkpoints:
    - data_quality: 数据准确性和完整性
    - code_quality: 代码规范和最佳实践
    - performance: 性能基准和效率
    - documentation: 文档完整性和清晰度

# 安装后消息
post_install_message: |
  🧬 Python科学计算扩展包安装完成！

  🎯 项目协调员: Dr. Alex Chen (python-scientific-orchestrator)
  📋 专家代理: 4位领域专家 (可视化、神经网络、信号处理)
  📝 智能模板: 4个带LLM指令嵌入的模板
  ✅ 质量保证: 多级验证系统和星级评分

  📊 专业领域:
  - 数据可视化: matplotlib, seaborn, plotly
  - 神经网络: TensorFlow, PyTorch, 模型优化
  - 信号处理: 滤波器设计, 频域分析, 降噪
  - 项目管理: 环境配置, 代码质量, 文档生成

  📁 必需用户数据文件 (放在 bmad-core/data/):
  - plot-preferences.yml: 可视化偏好设置
  - model-requirements.yml: 模型需求规格
  - signal-specifications.yml: 信号处理规格

  🚀 快速开始:
  1. 添加必需数据文件到 bmad-core/data/
  2. 运行: npm run agent python-scientific-orchestrator
  3. 跟随 Dr. Alex Chen 的编号选项指导
  4. 享受专业的Python科学计算开发体验！

  🎨 特色功能:
  - 角色人设: 每个代理都有独特的专业背景和沟通风格
  - 智能模板: LLM指令嵌入，条件内容和动态变量
  - 工作流编排: 决策树和交接协议，支持多种项目类型
  - 质量集成: 全面的验证系统，确保专业标准

  📚 嵌入知识库:
  - Python科学计算最佳实践和编码标准
  - 数据可视化设计指南和颜色理论
  - 神经网络架构模式和优化技巧
  - 信号处理算法参考和实现指导
