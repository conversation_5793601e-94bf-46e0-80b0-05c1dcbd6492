# Python科学计算扩展包计划

## 概述

- **包名**: python-scientific-computing
- **显示名称**: Python科学计算与数据分析扩展包
- **描述**: 专门用于Python科学计算、数据可视化、神经网络和数字信号处理的BMAD扩展包
- **目标领域**: 科学计算、数据科学、机器学习、信号处理
- **作者**: BMAD用户

## 问题陈述

该扩展包解决以下挑战：
- Python科学计算项目的标准化开发流程
- 数据可视化最佳实践和模板
- 神经网络模型开发的结构化方法
- 数字信号处理算法的实现指导
- 科学计算代码的质量保证和测试

## 目标用户

- 数据科学家和研究人员
- 机器学习工程师
- 信号处理专家
- 科学计算开发者
- 学术研究人员

## 要创建的组件

### 代理（带角色人设）

- [x] `python-scientific-orchestrator` - **必需**: Python科学计算项目协调员
  - 角色名称: Dr. Alex Chen (陈博士)
  - 沟通风格: 专业、技术导向、循序渐进
  - 主要命令: 项目初始化、工作流选择、质量检查、部署指导
  - 管理: 整个科学计算项目生命周期

- [x] `data-visualization-expert` - 数据可视化专家
  - 角色名称: Sarah Martinez (萨拉·马丁内斯)
  - 专长: matplotlib, seaborn, plotly, 交互式可视化
  - 人设: 创意丰富、注重美观和可读性
  - 使用任务: create-visualization, optimize-plots
  - 使用模板: visualization-report-tmpl
  - 需要数据: plot-preferences.yml

- [x] `neural-network-architect` - 神经网络架构师
  - 角色名称: Dr. Michael Zhang (张博士)
  - 专长: TensorFlow, PyTorch, 模型设计, 超参数优化
  - 人设: 严谨、系统性思维、注重性能
  - 使用任务: design-neural-network, optimize-model
  - 使用模板: model-architecture-tmpl
  - 需要数据: model-requirements.yml

- [x] `signal-processing-specialist` - 数字信号处理专家
  - 角色名称: Dr. Emily Rodriguez (罗德里格斯博士)
  - 专长: scipy.signal, 滤波器设计, 频域分析, 降噪
  - 人设: 精确、理论扎实、实用主义
  - 使用任务: design-filter, analyze-signal
  - 使用模板: signal-analysis-tmpl
  - 需要数据: signal-specifications.yml

### 任务

- [x] `create-visualization.md` - 创建科学数据可视化 (使用者: data-visualization-expert)
- [ ] `optimize-plots.md` - 优化图表性能和美观度 (使用者: data-visualization-expert)
- [x] `design-neural-network.md` - 设计神经网络架构 (使用者: neural-network-architect)
- [ ] `optimize-model.md` - 模型性能优化和调参 (使用者: neural-network-architect)
- [x] `design-filter.md` - 设计数字滤波器 (使用者: signal-processing-specialist)
- [ ] `analyze-signal.md` - 信号分析和处理 (使用者: signal-processing-specialist)
- [ ] `setup-python-environment.md` - 配置Python科学计算环境 (使用者: python-scientific-orchestrator)
- [x] `create-doc.md` - 从模板创建文档 (核心工具)
- [x] `execute-checklist.md` - 执行检查清单验证 (核心工具)

### 模板（带LLM指令嵌入）

- [x] `visualization-report-tmpl.md` - 数据可视化报告模板 (使用者: data-visualization-expert)
  - LLM指令: 逐步引导图表选择和设计
  - 条件内容: 基于数据类型的图表推荐
  - 变量: {{dataset_name}}, {{chart_type}}, {{analysis_goal}}

- [ ] `model-architecture-tmpl.md` - 神经网络架构文档模板 (使用者: neural-network-architect)
  - LLM指令: 引导模型设计决策和参数选择
  - 条件内容: 基于问题类型的架构建议
  - 变量: {{model_type}}, {{input_shape}}, {{output_classes}}

- [ ] `signal-analysis-tmpl.md` - 信号分析报告模板 (使用者: signal-processing-specialist)
  - LLM指令: 指导信号特征分析和处理方法
  - 条件内容: 基于信号类型的处理流程
  - 变量: {{signal_type}}, {{sampling_rate}}, {{analysis_method}}

- [ ] `python-project-structure-tmpl.md` - Python科学计算项目结构模板
  - LLM指令: 引导项目组织和最佳实践
  - 条件内容: 基于项目规模的结构建议
  - 变量: {{project_name}}, {{project_type}}, {{dependencies}}

### 检查清单（多级质量保证）

- [ ] `visualization-quality-checklist.md` - 数据可视化质量检查
  - 验证级别: 基础/全面/专家
  - 评分系统: 星级评分
  - 成功标准: 可读性、准确性、美观度

- [ ] `model-performance-checklist.md` - 神经网络模型性能检查
  - 验证级别: 基础/全面/专家
  - 评分系统: 准备就绪/未准备就绪
  - 成功标准: 准确率、泛化能力、计算效率

- [ ] `signal-processing-checklist.md` - 信号处理质量检查
  - 验证级别: 基础/全面/专家
  - 评分系统: 星级评分
  - 成功标准: 滤波效果、噪声抑制、频域特性

- [ ] `python-code-quality-checklist.md` - Python代码质量检查
  - 验证级别: 基础/全面/专家
  - 评分系统: 星级评分
  - 成功标准: PEP8规范、文档完整性、测试覆盖率

### 用户需要提供的数据文件

用户必须将这些文件添加到 `bmad-core/data/`:

- [ ] `plot-preferences.yml` - 可视化偏好设置
  - 格式: YAML配置文件
  - 目的: 定义默认图表样式、颜色方案、字体设置
  - 示例: 包含matplotlib和seaborn的样式配置

- [ ] `model-requirements.yml` - 模型需求规格
  - 格式: YAML配置文件
  - 目的: 定义模型性能要求、硬件限制、数据集信息
  - 示例: 准确率阈值、训练时间限制、内存使用限制

- [ ] `signal-specifications.yml` - 信号处理规格
  - 格式: YAML配置文件
  - 目的: 定义信号特性、处理要求、质量标准
  - 示例: 采样率、频率范围、噪声水平要求

### 嵌入的知识库数据

- [ ] `python-scientific-best-practices.md` - Python科学计算最佳实践
- [ ] `data-visualization-guidelines.md` - 数据可视化指南和标准
- [ ] `neural-network-design-patterns.md` - 神经网络设计模式
- [ ] `signal-processing-algorithms.md` - 常用信号处理算法参考

## 工作流概述

1. **项目初始化** - 协调员设置Python环境和项目结构
2. **需求分析** - 根据用户需求选择专家代理
3. **专家咨询** - 数据可视化/神经网络/信号处理专家提供指导
4. **实现开发** - 生成代码模板和配置文件
5. **质量检查** - 执行相应的质量检查清单
6. **文档生成** - 创建技术文档和报告
7. **部署准备** - 最终验证和部署指导

## 集成点

- 依赖核心代理: architect, developer, qa-specialist
- 扩展团队: 科学计算团队配置

## 成功标准

- [ ] 所有组件创建并交叉引用
- [ ] 无孤立的任务/模板引用
- [ ] 数据需求清晰记录
- [ ] 协调员提供清晰的工作流
- [ ] README包含设置说明
- [ ] 角色人设完整且一致
- [ ] 多级质量保证系统实现

## 用户批准

- [x] 用户已审查计划
- [x] 批准继续实施

---

**下一步**: 一旦获得批准，从协调员代理开始进行第3阶段实施。
