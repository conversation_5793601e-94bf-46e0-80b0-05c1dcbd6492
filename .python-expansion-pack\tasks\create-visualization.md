# 创建科学数据可视化任务

## 目的

指导用户创建专业的科学数据可视化，包括图表选择、样式设计和代码实现。

## 前提条件

- 用户已准备好数据或数据样本
- 明确可视化的目标和受众
- 了解数据的基本特征（类型、规模、分布等）

## 执行流程

### 1. 数据分析阶段

**询问用户以下信息：**

- 数据类型（数值型、分类型、时间序列等）
- 数据规模（样本数量、维度）
- 可视化目标（探索性分析、结果展示、比较分析等）
- 目标受众（技术专家、管理层、学术论文等）
- 输出格式要求（静态图片、交互式图表、报告嵌入等）

### 2. 图表类型推荐

**根据数据特征推荐最适合的图表类型：**

**单变量数据：**
- 连续数据：直方图、密度图、箱线图
- 分类数据：柱状图、饼图、条形图

**双变量数据：**
- 连续-连续：散点图、线图、回归图
- 连续-分类：箱线图、小提琴图、条形图
- 分类-分类：堆叠柱状图、热力图、马赛克图

**多变量数据：**
- 相关性分析：相关矩阵热力图、散点图矩阵
- 降维可视化：PCA图、t-SNE图
- 多维比较：平行坐标图、雷达图

**时间序列：**
- 趋势分析：线图、面积图
- 周期性分析：季节性分解图
- 多序列比较：多线图、堆叠面积图

### 3. 样式设计

**应用科学可视化最佳实践：**

- 选择合适的颜色方案（色盲友好、打印友好）
- 设置清晰的标题、轴标签和图例
- 调整字体大小和样式以确保可读性
- 添加网格线和参考线提高可读性
- 确保图表比例和尺寸适合输出媒介

### 4. 代码实现

**生成完整的Python代码：**

```python
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 创建图表
fig, ax = plt.subplots(figsize=(10, 6))

# 绘制数据
# [具体绘图代码根据选择的图表类型生成]

# 设置标题和标签
ax.set_title('图表标题', fontsize=16, fontweight='bold')
ax.set_xlabel('X轴标签', fontsize=12)
ax.set_ylabel('Y轴标签', fontsize=12)

# 添加图例和网格
ax.legend()
ax.grid(True, alpha=0.3)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('output_chart.png', dpi=300, bbox_inches='tight')
plt.show()
```

### 5. 质量检查

**验证可视化质量：**

- 数据准确性：确保图表正确反映数据
- 可读性：标签清晰、颜色对比度适当
- 美观性：布局合理、样式专业
- 信息传达：图表能够有效传达预期信息
- 技术规范：代码可运行、注释完整

### 6. 优化建议

**提供改进建议：**

- 交互性增强（使用plotly或bokeh）
- 动画效果（适用于时间序列或过程展示）
- 多子图布局（比较多个相关图表）
- 自定义主题（符合品牌或出版要求）
- 性能优化（大数据集的处理策略）

## 输出交付物

1. **可视化代码**：完整的Python脚本
2. **样式配置**：颜色、字体、布局设置
3. **使用说明**：如何运行代码和自定义参数
4. **质量检查报告**：验证图表质量的检查清单
5. **改进建议**：进一步优化的方向

## 常见问题解决

**数据量过大：**
- 使用采样技术
- 考虑聚合或分组
- 使用交互式工具处理大数据

**颜色选择困难：**
- 使用ColorBrewer或seaborn调色板
- 考虑色盲友好的配色方案
- 测试黑白打印效果

**图表过于复杂：**
- 简化信息层次
- 考虑分解为多个子图
- 使用注释和标注突出重点

## 质量标准

- 图表能够在3秒内被理解
- 所有文字清晰可读
- 颜色使用有意义且一致
- 数据完整且准确
- 代码可重现且有注释
