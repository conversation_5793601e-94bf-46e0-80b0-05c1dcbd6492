# 设计数字滤波器任务

## 目的

指导用户设计和实现各类数字滤波器，包括滤波器类型选择、参数设计和性能验证。

## 前提条件

- 了解信号的基本特征（采样率、频率成分、噪声特性）
- 明确滤波目标和性能要求
- 掌握基本的信号处理理论
- 安装scipy、numpy、matplotlib等必要库

## 执行流程

### 1. 信号分析阶段

**收集信号信息：**

- **信号特征**：
  - 采样率（fs）
  - 信号频率范围
  - 噪声类型和频率分布
  - 信号长度和实时性要求
- **滤波目标**：
  - 保留的频率范围
  - 抑制的频率范围
  - 允许的失真程度
  - 相位响应要求

### 2. 滤波器类型选择

**根据需求选择滤波器类型：**

**按频率响应分类：**
- **低通滤波器**：保留低频，抑制高频
- **高通滤波器**：保留高频，抑制低频
- **带通滤波器**：保留特定频带
- **带阻滤波器**：抑制特定频带

**按实现方式分类：**
- **FIR滤波器**：有限冲激响应，线性相位
- **IIR滤波器**：无限冲激响应，计算效率高

### 3. 滤波器设计

**FIR滤波器设计：**

```python
import numpy as np
import scipy.signal as signal
import matplotlib.pyplot as plt

def design_fir_filter(fs, cutoff, filter_type='lowpass', numtaps=101):
    """
    设计FIR滤波器
    
    参数:
    fs: 采样频率
    cutoff: 截止频率（对于带通/带阻，传入[low, high]）
    filter_type: 'lowpass', 'highpass', 'bandpass', 'bandstop'
    numtaps: 滤波器阶数
    """
    nyquist = fs / 2
    
    if filter_type in ['bandpass', 'bandstop']:
        normal_cutoff = [f / nyquist for f in cutoff]
    else:
        normal_cutoff = cutoff / nyquist
    
    # 使用窗函数法设计FIR滤波器
    b = signal.firwin(numtaps, normal_cutoff, 
                      window='hamming', 
                      pass_zero=(filter_type in ['lowpass', 'bandstop']))
    
    return b, [1.0]  # FIR滤波器的分母系数为1
```

**IIR滤波器设计：**

```python
def design_iir_filter(fs, cutoff, filter_type='lowpass', order=5, ftype='butter'):
    """
    设计IIR滤波器
    
    参数:
    fs: 采样频率
    cutoff: 截止频率
    filter_type: 'lowpass', 'highpass', 'bandpass', 'bandstop'
    order: 滤波器阶数
    ftype: 'butter', 'cheby1', 'cheby2', 'ellip'
    """
    nyquist = fs / 2
    normal_cutoff = cutoff / nyquist
    
    if ftype == 'butter':
        b, a = signal.butter(order, normal_cutoff, btype=filter_type)
    elif ftype == 'cheby1':
        b, a = signal.cheby1(order, 1, normal_cutoff, btype=filter_type)
    elif ftype == 'cheby2':
        b, a = signal.cheby2(order, 40, normal_cutoff, btype=filter_type)
    elif ftype == 'ellip':
        b, a = signal.ellip(order, 1, 40, normal_cutoff, btype=filter_type)
    
    return b, a
```

### 4. 滤波器分析

**频率响应分析：**

```python
def analyze_filter(b, a, fs):
    """分析滤波器频率响应"""
    # 计算频率响应
    w, h = signal.freqz(b, a, worN=8000)
    frequencies = w * fs / (2 * np.pi)
    
    # 绘制幅度响应
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(frequencies, 20 * np.log10(abs(h)))
    plt.title('幅度响应')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅度 (dB)')
    plt.grid(True)
    
    # 绘制相位响应
    plt.subplot(2, 2, 2)
    plt.plot(frequencies, np.angle(h))
    plt.title('相位响应')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('相位 (弧度)')
    plt.grid(True)
    
    # 绘制群延迟
    plt.subplot(2, 2, 3)
    w, gd = signal.group_delay((b, a))
    plt.plot(w * fs / (2 * np.pi), gd)
    plt.title('群延迟')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('延迟 (样本)')
    plt.grid(True)
    
    # 绘制零极点图
    plt.subplot(2, 2, 4)
    zeros, poles, _ = signal.tf2zpk(b, a)
    plt.scatter(np.real(zeros), np.imag(zeros), marker='o', label='零点')
    plt.scatter(np.real(poles), np.imag(poles), marker='x', label='极点')
    plt.xlabel('实部')
    plt.ylabel('虚部')
    plt.title('零极点图')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return frequencies, h
```

### 5. 滤波器应用

**信号滤波实现：**

```python
def apply_filter(signal_data, b, a, method='filtfilt'):
    """
    应用滤波器到信号
    
    参数:
    signal_data: 输入信号
    b, a: 滤波器系数
    method: 'lfilter', 'filtfilt'（零相位滤波）
    """
    if method == 'filtfilt':
        # 零相位滤波，无相位失真
        filtered_signal = signal.filtfilt(b, a, signal_data)
    else:
        # 标准滤波
        filtered_signal = signal.lfilter(b, a, signal_data)
    
    return filtered_signal
```

### 6. 性能评估

**滤波效果评估：**

```python
def evaluate_filter_performance(original, filtered, fs):
    """评估滤波器性能"""
    # 计算信噪比改善
    noise_power_before = np.var(original)
    noise_power_after = np.var(filtered)
    snr_improvement = 10 * np.log10(noise_power_before / noise_power_after)
    
    # 频谱比较
    f_orig, psd_orig = signal.welch(original, fs)
    f_filt, psd_filt = signal.welch(filtered, fs)
    
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(np.arange(len(original)) / fs, original, label='原始信号')
    plt.plot(np.arange(len(filtered)) / fs, filtered, label='滤波后信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.title('时域比较')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.semilogy(f_orig, psd_orig, label='原始信号')
    plt.semilogy(f_filt, psd_filt, label='滤波后信号')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('功率谱密度')
    plt.title('频域比较')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print(f"信噪比改善: {snr_improvement:.2f} dB")
    
    return snr_improvement
```

### 7. 优化建议

**滤波器优化策略：**

- **阶数选择**：平衡滤波效果和计算复杂度
- **窗函数选择**：影响旁瓣抑制和过渡带宽度
- **相位响应**：选择线性相位或最小相位
- **稳定性检查**：确保IIR滤波器稳定
- **实时处理**：考虑延迟和计算效率

## 输出交付物

1. **滤波器系数**：b和a系数数组
2. **设计代码**：完整的滤波器设计脚本
3. **分析图表**：频率响应、相位响应、零极点图
4. **应用示例**：滤波器应用到实际信号的代码
5. **性能报告**：滤波效果评估结果
6. **使用说明**：参数调整和优化建议

## 常见滤波器类型

**巴特沃斯滤波器**：平坦的通带响应
**切比雪夫滤波器**：陡峭的过渡带
**椭圆滤波器**：最优的过渡带特性
**贝塞尔滤波器**：线性相位响应

## 质量检查要点

- 频率响应符合设计要求
- 相位响应满足应用需求
- 滤波器稳定性验证
- 计算复杂度评估
- 实际信号测试效果
