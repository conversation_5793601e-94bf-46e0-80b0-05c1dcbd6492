# 设计神经网络架构任务

## 目的

指导用户设计适合特定问题的神经网络架构，包括网络结构选择、参数配置和实现代码。

## 前提条件

- 明确定义的机器学习问题
- 了解数据的基本特征和规模
- 确定性能要求和约束条件
- 选择深度学习框架（TensorFlow/Keras或PyTorch）

## 执行流程

### 1. 问题分析阶段

**收集关键信息：**

- **问题类型**：分类、回归、序列预测、生成模型等
- **数据特征**：
  - 输入维度和类型（图像、文本、数值、序列）
  - 输出维度和类型
  - 数据集大小（训练/验证/测试）
  - 数据质量和预处理需求
- **性能要求**：
  - 准确率/精度目标
  - 推理速度要求
  - 内存和计算资源限制
  - 可解释性需求

### 2. 架构选择

**根据问题类型推荐基础架构：**

**图像处理：**
- CNN（卷积神经网络）
- ResNet（残差网络）
- EfficientNet（高效网络）
- Vision Transformer（视觉变换器）

**序列数据：**
- RNN/LSTM/GRU（循环神经网络）
- Transformer（注意力机制）
- 1D CNN（一维卷积）

**表格数据：**
- 全连接网络（MLP）
- TabNet（表格专用网络）
- 集成方法结合深度学习

**多模态数据：**
- 多输入网络
- 注意力融合机制
- 预训练模型微调

### 3. 网络结构设计

**详细设计网络层次：**

```python
# 示例：图像分类CNN架构
import tensorflow as tf
from tensorflow.keras import layers, models

def create_cnn_model(input_shape, num_classes):
    model = models.Sequential([
        # 卷积块1
        layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # 卷积块2
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # 卷积块3
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # 全连接层
        layers.Flatten(),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    return model
```

### 4. 超参数配置

**关键超参数设置：**

- **学习率**：初始学习率和调度策略
- **批次大小**：根据内存和收敛性选择
- **优化器**：Adam、SGD、RMSprop等
- **正则化**：Dropout、L1/L2正则化、批归一化
- **激活函数**：ReLU、Leaky ReLU、Swish等

### 5. 训练策略

**制定训练计划：**

```python
# 编译模型
model.compile(
    optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# 回调函数
callbacks = [
    tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
    tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5),
    tf.keras.callbacks.ModelCheckpoint('best_model.h5', save_best_only=True)
]

# 训练模型
history = model.fit(
    train_data,
    epochs=100,
    validation_data=val_data,
    callbacks=callbacks,
    batch_size=32
)
```

### 6. 模型评估

**全面评估模型性能：**

- **准确性指标**：准确率、精确率、召回率、F1分数
- **损失分析**：训练损失和验证损失曲线
- **过拟合检测**：训练集和验证集性能差异
- **混淆矩阵**：详细的分类性能分析
- **计算效率**：训练时间、推理速度、模型大小

### 7. 优化建议

**性能改进策略：**

**准确性优化：**
- 数据增强技术
- 集成学习方法
- 超参数调优
- 架构搜索

**效率优化：**
- 模型剪枝
- 量化技术
- 知识蒸馏
- 架构优化

**泛化能力：**
- 交叉验证
- 正则化技术
- 数据多样性
- 迁移学习

## 输出交付物

1. **网络架构图**：可视化的网络结构
2. **实现代码**：完整的模型定义和训练代码
3. **超参数配置**：详细的参数设置和理由
4. **训练脚本**：包含数据加载、训练、评估的完整流程
5. **性能基准**：预期性能指标和评估方法
6. **优化建议**：进一步改进的方向

## 常见架构模式

**残差连接**：解决深层网络梯度消失问题
**注意力机制**：提高模型对重要特征的关注
**批归一化**：加速训练并提高稳定性
**Dropout**：防止过拟合
**数据增强**：增加数据多样性

## 质量检查要点

- 架构与问题匹配度
- 参数数量合理性
- 计算复杂度评估
- 内存使用估算
- 训练稳定性
- 可重现性保证

## 部署考虑

- 模型序列化和保存
- 推理优化
- 硬件兼容性
- 版本管理
- 监控和维护
