# {{project_name}} 数据可视化报告

[[LLM: 开始时询问用户项目名称、数据类型和可视化目标，然后逐步完成每个部分]]

## 项目概述

**项目名称**: {{project_name}}  
**分析日期**: {{analysis_date}}  
**分析师**: {{analyst_name}}  
**数据来源**: {{data_source}}

[[LLM: 询问用户数据的基本信息，包括数据来源、时间范围、样本数量等]]

### 分析目标

{{analysis_objectives}}

[[LLM: 让用户描述可视化的具体目标，例如：探索性数据分析、结果展示、趋势发现等]]

## 数据概览

### 数据集描述

- **数据规模**: {{data_size}}
- **时间范围**: {{time_range}}
- **主要变量**: {{main_variables}}
- **数据质量**: {{data_quality_notes}}

[[LLM: 根据用户提供的数据信息，生成数据集的基本统计描述]]

### 数据预处理

^^CONDITION: data_preprocessing_needed^^
**预处理步骤**:
1. {{preprocessing_step_1}}
2. {{preprocessing_step_2}}
3. {{preprocessing_step_3}}

**处理后数据特征**:
- 缺失值处理: {{missing_value_treatment}}
- 异常值处理: {{outlier_treatment}}
- 数据转换: {{data_transformation}}
^^/CONDITION: data_preprocessing_needed^^

[[LLM: 如果需要数据预处理，询问具体的预处理步骤和方法]]

## 可视化分析

### 主要发现

[[LLM: 基于用户的数据和分析目标，引导用户描述主要发现]]

1. **{{finding_1_title}}**
   - 描述: {{finding_1_description}}
   - 支持图表: {{supporting_chart_1}}

2. **{{finding_2_title}}**
   - 描述: {{finding_2_description}}
   - 支持图表: {{supporting_chart_2}}

3. **{{finding_3_title}}**
   - 描述: {{finding_3_description}}
   - 支持图表: {{supporting_chart_3}}

### 图表详细分析

<<REPEAT section="chart_analysis" count="{{chart_count}}">>

#### 图表 {{chart_number}}: {{chart_title}}

**图表类型**: {{chart_type}}  
**数据变量**: {{chart_variables}}  
**关键洞察**: {{chart_insights}}

```python
# 图表生成代码
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

# 设置图表样式
plt.style.use('{{plot_style}}')
sns.set_palette("{{color_palette}}")

# 创建图表
fig, ax = plt.subplots(figsize=({{figure_width}}, {{figure_height}}))

# 绘制数据
{{chart_code}}

# 设置标题和标签
ax.set_title('{{chart_title}}', fontsize={{title_fontsize}}, fontweight='bold')
ax.set_xlabel('{{x_label}}', fontsize={{label_fontsize}})
ax.set_ylabel('{{y_label}}', fontsize={{label_fontsize}})

# 添加图例和网格
^^CONDITION: show_legend^^
ax.legend(fontsize={{legend_fontsize}})
^^/CONDITION: show_legend^^

^^CONDITION: show_grid^^
ax.grid(True, alpha={{grid_alpha}})
^^/CONDITION: show_grid^^

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('{{chart_filename}}.png', dpi={{dpi}}, bbox_inches='tight')
plt.show()
```

**解读说明**:
{{chart_interpretation}}

<</REPEAT>>

[[LLM: 对每个图表，询问用户图表类型、使用的变量、期望的样式设置，然后生成相应的代码和解读]]

## 统计分析

^^CONDITION: statistical_analysis_included^^

### 描述性统计

{{descriptive_statistics}}

### 相关性分析

^^CONDITION: correlation_analysis^^
**主要相关性发现**:
- {{correlation_finding_1}}
- {{correlation_finding_2}}
- {{correlation_finding_3}}

```python
# 相关性矩阵热力图
correlation_matrix = data[{{correlation_variables}}].corr()
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='{{correlation_colormap}}', 
            center=0, square=True, linewidths=0.5)
plt.title('变量相关性矩阵')
plt.tight_layout()
plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
plt.show()
```
^^/CONDITION: correlation_analysis^^

### 趋势分析

^^CONDITION: trend_analysis^^
**趋势特征**:
- 总体趋势: {{overall_trend}}
- 季节性模式: {{seasonal_pattern}}
- 异常点: {{anomalies}}

```python
# 趋势分析图
plt.figure(figsize=(12, 6))
plt.plot({{time_variable}}, {{value_variable}}, linewidth=2)
plt.title('{{trend_title}}')
plt.xlabel('{{time_label}}')
plt.ylabel('{{value_label}}')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('trend_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
```
^^/CONDITION: trend_analysis^^

^^/CONDITION: statistical_analysis_included^^

[[LLM: 根据数据类型和分析需求，决定是否包含统计分析部分]]

## 技术规格

### 使用的工具和库

- **Python版本**: {{python_version}}
- **主要库**:
  - matplotlib: {{matplotlib_version}}
  - seaborn: {{seaborn_version}}
  - pandas: {{pandas_version}}
  - numpy: {{numpy_version}}

### 图表配置

- **默认图表尺寸**: {{default_figure_size}}
- **DPI设置**: {{dpi_setting}}
- **颜色方案**: {{color_scheme}}
- **字体设置**: {{font_settings}}

```python
# 全局样式配置
import matplotlib.pyplot as plt
import seaborn as sns

# 设置matplotlib参数
plt.rcParams['figure.figsize'] = {{default_figure_size}}
plt.rcParams['font.size'] = {{default_font_size}}
plt.rcParams['axes.titlesize'] = {{title_font_size}}
plt.rcParams['axes.labelsize'] = {{label_font_size}}
plt.rcParams['xtick.labelsize'] = {{tick_font_size}}
plt.rcParams['ytick.labelsize'] = {{tick_font_size}}
plt.rcParams['legend.fontsize'] = {{legend_font_size}}

# 设置seaborn样式
sns.set_style("{{seaborn_style}}")
sns.set_palette("{{seaborn_palette}}")
```

## 结论与建议

### 主要结论

[[LLM: 基于所有分析结果，帮助用户总结主要结论]]

1. {{conclusion_1}}
2. {{conclusion_2}}
3. {{conclusion_3}}

### 后续分析建议

^^CONDITION: further_analysis_needed^^
**建议的后续分析**:
- {{suggestion_1}}
- {{suggestion_2}}
- {{suggestion_3}}
^^/CONDITION: further_analysis_needed^^

### 可视化改进建议

- **交互性增强**: {{interactivity_suggestions}}
- **动画效果**: {{animation_suggestions}}
- **多维展示**: {{multidimensional_suggestions}}

## 附录

### 完整代码

```python
# 完整的可视化分析代码
# 可以直接运行生成所有图表

{{complete_analysis_code}}
```

### 数据字典

^^CONDITION: data_dictionary_needed^^
| 变量名 | 类型 | 描述 | 取值范围 |
|--------|------|------|----------|
{{data_dictionary_table}}
^^/CONDITION: data_dictionary_needed^^

---

**报告生成时间**: {{report_generation_time}}  
**版本**: {{report_version}}

[[LLM: 在完成报告后，询问用户是否需要调整任何部分，或者添加额外的分析内容]]
