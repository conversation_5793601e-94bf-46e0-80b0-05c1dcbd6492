# Python科学计算工作流

## 工作流概述

本工作流指导用户完成Python科学计算项目的完整开发过程，从项目初始化到最终部署。

## 决策树

```mermaid
flowchart TD
    A[开始Python科学计算项目] --> B{项目类型?}
    
    B -->|数据可视化| C[数据可视化工作流]
    B -->|机器学习| D[神经网络工作流]
    B -->|信号处理| E[信号处理工作流]
    B -->|综合项目| F[完整科学计算工作流]
    
    C --> C1[<PERSON> Martinez - 可视化专家]
    C1 --> C2[创建可视化]
    C2 --> C3[质量检查]
    C3 --> G[项目完成]
    
    D --> D1[Dr. <PERSON> Zhang - 神经网络架构师]
    D1 --> D2[设计模型架构]
    D2 --> D3[模型训练和优化]
    D3 --> D4[性能评估]
    D4 --> G
    
    E --> E1[Dr. <PERSON> Rodriguez - 信号处理专家]
    E1 --> E2[信号分析]
    E2 --> E3[滤波器设计]
    E3 --> E4[处理效果验证]
    E4 --> G
    
    F --> F1[Dr. <PERSON> Chen - 项目协调员]
    F1 --> F2[需求分析]
    F2 --> F3{主要任务?}
    F3 -->|可视化为主| C1
    F3 -->|建模为主| D1
    F3 -->|信号处理为主| E1
    F3 -->|多任务| F4[多专家协作]
    F4 --> F5[集成和验证]
    F5 --> G
```

## 工作流路径

### 路径1: 数据可视化项目

^^CONDITION: project_type == "visualization"^^

**阶段1: 项目初始化**
- 协调员: Dr. Alex Chen
- 任务: 项目环境设置和需求分析
- 输出: 项目结构、环境配置

**阶段2: 数据分析和可视化设计**
- 专家: Sarah Martinez (数据可视化专家)
- 任务: 数据探索、图表类型选择、样式设计
- 输出: 可视化方案、样式指南

**阶段3: 可视化实现**
- 专家: Sarah Martinez
- 任务: 代码实现、图表生成
- 输出: 可视化代码、图表文件

**阶段4: 质量检查和优化**
- 任务: 执行可视化质量检查清单
- 输出: 质量报告、改进建议

**阶段5: 文档和交付**
- 任务: 生成可视化报告
- 输出: 完整的可视化报告、使用文档

^^/CONDITION: project_type^^

### 路径2: 神经网络项目

^^CONDITION: project_type == "neural_network"^^

**阶段1: 问题定义和数据准备**
- 协调员: Dr. Alex Chen
- 任务: 问题分析、数据预处理
- 输出: 问题定义、清洁数据

**阶段2: 模型架构设计**
- 专家: Dr. Michael Zhang (神经网络架构师)
- 任务: 架构选择、网络设计
- 输出: 模型架构文档、设计代码

**阶段3: 模型实现和训练**
- 专家: Dr. Michael Zhang
- 任务: 模型编码、训练策略、超参数调优
- 输出: 训练代码、训练好的模型

**阶段4: 模型评估和优化**
- 专家: Dr. Michael Zhang
- 任务: 性能评估、模型优化
- 输出: 评估报告、优化建议

**阶段5: 模型验证和部署准备**
- 任务: 执行模型性能检查清单
- 输出: 验证报告、部署方案

^^/CONDITION: project_type^^

### 路径3: 信号处理项目

^^CONDITION: project_type == "signal_processing"^^

**阶段1: 信号特征分析**
- 协调员: Dr. Alex Chen
- 任务: 信号采集、基本特征分析
- 输出: 信号特征报告

**阶段2: 处理方案设计**
- 专家: Dr. Emily Rodriguez (信号处理专家)
- 任务: 分析需求、选择处理方法
- 输出: 处理方案、算法选择

**阶段3: 滤波器设计和实现**
- 专家: Dr. Emily Rodriguez
- 任务: 滤波器设计、参数优化
- 输出: 滤波器代码、参数配置

**阶段4: 信号处理和分析**
- 专家: Dr. Emily Rodriguez
- 任务: 信号处理、特征提取、结果分析
- 输出: 处理后信号、分析结果

**阶段5: 效果验证和报告**
- 任务: 执行信号处理质量检查
- 输出: 处理效果报告、质量评估

^^/CONDITION: project_type^^

### 路径4: 综合科学计算项目

^^CONDITION: project_type == "comprehensive"^^

**阶段1: 项目规划和架构设计**
- 协调员: Dr. Alex Chen
- 任务: 全面需求分析、项目架构设计
- 输出: 项目计划、技术架构

**阶段2: 多专家协作阶段**

**子阶段2a: 数据可视化**
- 专家: Sarah Martinez
- 任务: 探索性数据分析、可视化设计
- 输出: 数据洞察、可视化组件

**子阶段2b: 模型开发**
- 专家: Dr. Michael Zhang
- 任务: 机器学习模型开发
- 输出: 预测模型、性能评估

**子阶段2c: 信号处理**
- 专家: Dr. Emily Rodriguez
- 任务: 信号预处理、特征工程
- 输出: 处理后数据、特征集

**阶段3: 系统集成**
- 协调员: Dr. Alex Chen
- 任务: 组件集成、系统测试
- 输出: 集成系统、测试报告

**阶段4: 全面质量检查**
- 任务: 执行所有相关检查清单
- 输出: 综合质量报告

**阶段5: 文档和部署**
- 任务: 完整文档生成、部署准备
- 输出: 项目文档、部署包

^^/CONDITION: project_type^^

## 质量门控

每个阶段都包含质量检查点：

### 检查点1: 数据质量
- 数据完整性验证
- 数据清洗质量检查
- 数据格式标准化

### 检查点2: 代码质量
- 代码规范检查
- 单元测试覆盖
- 性能基准测试

### 检查点3: 结果质量
- 准确性验证
- 可重现性检查
- 文档完整性

### 检查点4: 部署就绪
- 环境兼容性
- 性能要求满足
- 安全性检查

## 代理协作协议

### 代理间交接

**从协调员到专家**:
```
交接信息包:
- 项目背景和目标
- 数据特征和约束
- 性能要求
- 时间安排
```

**专家间协作**:
```
协作信息:
- 前序工作成果
- 接口规范
- 依赖关系
- 质量标准
```

**回到协调员**:
```
汇报内容:
- 完成状态
- 质量评估
- 遇到的问题
- 后续建议
```

## 工作流自定义

### 项目规模调整

**小型项目** (< 1周):
- 简化文档要求
- 合并质量检查
- 单专家主导

**中型项目** (1-4周):
- 标准工作流
- 完整质量检查
- 多专家协作

**大型项目** (> 4周):
- 增加里程碑检查
- 详细进度跟踪
- 风险管理

### 领域特定调整

**学术研究**:
- 强调可重现性
- 详细的方法论文档
- 统计严谨性

**工业应用**:
- 重视性能和效率
- 部署和维护考虑
- 成本效益分析

**原型开发**:
- 快速迭代
- 概念验证优先
- 灵活的质量标准

## 成功标准

### 项目完成标准
- [ ] 所有阶段任务完成
- [ ] 质量检查通过
- [ ] 文档完整
- [ ] 代码可运行
- [ ] 结果可重现

### 质量标准
- [ ] 代码符合PEP 8规范
- [ ] 测试覆盖率 > 80%
- [ ] 文档完整性 > 90%
- [ ] 性能满足要求
- [ ] 安全性检查通过

---

**工作流版本**: v1.0  
**最后更新**: 2024年7月
