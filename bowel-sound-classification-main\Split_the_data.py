"""
数据分割工具函数
专门用于音频数据的分层分组分割
"""

import pandas as pd
import numpy as np
import os
from sklearn.model_selection import GroupShuffleSplit
from sklearn.preprocessing import LabelEncoder


def split_wave_data_with_stratified_groups(csv_file, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
    """
    使用分层分组分割音频数据
    确保同一患者的数据不会同时出现在训练集和测试集中
    
    参数:
        csv_file: CSV文件路径
        train_ratio: 训练集比例
        val_ratio: 验证集比例  
        test_ratio: 测试集比例
    
    返回:
        X_train, X_val, X_test, y_train, y_val, y_test (文件路径列表)
    """
    
    # 读取数据
    df = pd.read_csv(csv_file)
    
    # 确保必要的列存在
    required_cols = ['path', 'label']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"CSV文件中缺少必需的列: {col}")
    
    # 提取患者ID（从文件路径中提取）
    if 'patient_id' not in df.columns:
        df['patient_id'] = df['path'].apply(extract_patient_id)
    
    # 编码标签
    le = LabelEncoder()
    df['label_encoded'] = le.fit_transform(df['label'])
    
    # 执行分层分组分割
    X_train, X_val, X_test, y_train, y_val, y_test = _stratified_group_split(
        df, train_ratio, val_ratio, test_ratio
    )
    
    return X_train, X_val, X_test, y_train, y_val, y_test


def extract_patient_id(file_path):
    """
    从文件路径中提取患者ID
    假设文件名格式为: {patient_id}_{其他信息}.wav
    """
    filename = os.path.basename(file_path)
    # 移除扩展名
    filename_no_ext = os.path.splitext(filename)[0]
    # 提取患者ID（假设是第一个下划线之前的部分）
    patient_id = filename_no_ext.split('_')[0]
    return patient_id


def _stratified_group_split(df, train_ratio, val_ratio, test_ratio):
    """
    执行分层分组分割
    """
    # 按患者和标签分组，计算每个患者的主要标签
    patient_stats = df.groupby(['patient_id', 'label_encoded']).size().reset_index(name='count')
    patient_main_label = patient_stats.loc[patient_stats.groupby('patient_id')['count'].idxmax()]
    
    # 获取患者列表和对应的主要标签
    patients = patient_main_label['patient_id'].values
    labels = patient_main_label['label_encoded'].values
    
    # 使用GroupShuffleSplit进行分割
    # 第一次分割：训练集 vs (验证集+测试集)
    gss1 = GroupShuffleSplit(n_splits=1, test_size=(val_ratio + test_ratio), random_state=42)
    train_idx, temp_idx = next(gss1.split(patients, labels, groups=patients))
    
    train_patients = patients[train_idx]
    temp_patients = patients[temp_idx]
    temp_labels = labels[temp_idx]
    
    # 第二次分割：验证集 vs 测试集
    if val_ratio > 0 and len(temp_patients) > 1:
        val_test_ratio = test_ratio / (val_ratio + test_ratio)
        gss2 = GroupShuffleSplit(n_splits=1, test_size=val_test_ratio, random_state=42)
        val_idx, test_idx = next(gss2.split(temp_patients, temp_labels, groups=temp_patients))
        
        val_patients = temp_patients[val_idx]
        test_patients = temp_patients[test_idx]
    else:
        val_patients = np.array([])
        test_patients = temp_patients
    
    # 根据患者ID分割数据
    train_data = df[df['patient_id'].isin(train_patients)]
    val_data = df[df['patient_id'].isin(val_patients)] if len(val_patients) > 0 else pd.DataFrame()
    test_data = df[df['patient_id'].isin(test_patients)]
    
    # 提取文件路径和标签
    X_train = train_data['path'].tolist()
    y_train = train_data['label'].tolist()
    
    X_val = val_data['path'].tolist() if len(val_data) > 0 else []
    y_val = val_data['label'].tolist() if len(val_data) > 0 else []
    
    X_test = test_data['path'].tolist()
    y_test = test_data['label'].tolist()
    
    # 打印分割信息
    print_split_summary(train_data, val_data, test_data, train_patients, val_patients, test_patients)
    
    return X_train, X_val, X_test, y_train, y_val, y_test


def print_split_summary(train_data, val_data, test_data, train_patients, val_patients, test_patients):
    """
    打印数据分割摘要信息
    """
    print("=== 音频数据分割摘要 ===")
    print(f"训练集: {len(train_data)} 个音频文件, {len(train_patients)} 个患者")
    print(f"验证集: {len(val_data)} 个音频文件, {len(val_patients)} 个患者")
    print(f"测试集: {len(test_data)} 个音频文件, {len(test_patients)} 个患者")
    
    total_files = len(train_data) + len(val_data) + len(test_data)
    total_patients = len(train_patients) + len(val_patients) + len(test_patients)
    print(f"总计: {total_files} 个音频文件, {total_patients} 个患者")
    
    print("\n=== 标签分布 ===")
    if len(train_data) > 0:
        print("训练集:")
        print(train_data['label'].value_counts().sort_index())
    
    if len(val_data) > 0:
        print("验证集:")
        print(val_data['label'].value_counts().sort_index())
    
    if len(test_data) > 0:
        print("测试集:")
        print(test_data['label'].value_counts().sort_index())


def validate_split(X_train, X_val, X_test, y_train, y_val, y_test):
    """
    验证数据分割的正确性
    """
    # 检查是否有重叠
    train_set = set(X_train)
    val_set = set(X_val)
    test_set = set(X_test)
    
    train_val_overlap = train_set.intersection(val_set)
    train_test_overlap = train_set.intersection(test_set)
    val_test_overlap = val_set.intersection(test_set)
    
    if train_val_overlap:
        print(f"警告: 训练集和验证集有重叠文件: {len(train_val_overlap)} 个")
    
    if train_test_overlap:
        print(f"警告: 训练集和测试集有重叠文件: {len(train_test_overlap)} 个")
    
    if val_test_overlap:
        print(f"警告: 验证集和测试集有重叠文件: {len(val_test_overlap)} 个")
    
    if not (train_val_overlap or train_test_overlap or val_test_overlap):
        print("✓ 数据分割验证通过，各集合间无重叠")
    
    # 检查标签数量匹配
    assert len(X_train) == len(y_train), "训练集特征和标签数量不匹配"
    assert len(X_val) == len(y_val), "验证集特征和标签数量不匹配"
    assert len(X_test) == len(y_test), "测试集特征和标签数量不匹配"
    
    print("✓ 特征和标签数量匹配验证通过")


def save_split_info(X_train, X_val, X_test, y_train, y_val, y_test, output_dir="./"):
    """
    保存数据分割信息到文件
    """
    import json
    
    split_info = {
        'train': {'files': X_train, 'labels': y_train, 'count': len(X_train)},
        'val': {'files': X_val, 'labels': y_val, 'count': len(X_val)},
        'test': {'files': X_test, 'labels': y_test, 'count': len(X_test)}
    }
    
    output_file = os.path.join(output_dir, 'data_split_info.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(split_info, f, indent=2, ensure_ascii=False)
    
    print(f"数据分割信息已保存到: {output_file}")
