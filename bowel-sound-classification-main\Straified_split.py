"""
分层数据分割工具
实现基于患者ID的分层分割，确保同一患者的数据不会同时出现在训练集和测试集中
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder


def split_stratified(df, subset_ratios=[0.7, 0.15, 0.15], filename="subset.txt", 
                    col_subset="subset", col_index=None, col_label="label"):
    """
    执行分层数据分割，确保类别平衡和患者分组
    如果某些标签只存在于少数患者中，则改用样本级分割
    
    参数:
        df: 包含数据的DataFrame
        subset_ratios: 分割比例 [train, val, test]
        filename: 输出文件名（用于记录分割信息）
        col_subset: 子集列名
        col_index: 索引列名
        col_label: 标签列名
    
    返回:
        X_train, y_train, X_val, y_val, X_test, y_test
    """
    
    # 确保比例总和为1
    assert abs(sum(subset_ratios) - 1.0) < 1e-6, "分割比例总和必须为1"
    
    train_ratio, val_ratio, test_ratio = subset_ratios
    
    # 检查数据集的复杂度
    if 'patient_id' in df.columns:
        # 分析标签和患者的分布
        unique_labels = df[col_label].unique()
        unique_patients = df['patient_id'].unique()
        
        print(f"数据集分析: {len(unique_labels)} 个标签, {len(unique_patients)} 个患者")
        
        # 检查每个标签对应的患者数量
        problematic_labels = []
        for label in unique_labels:
            patients_with_label = df[df[col_label] == label]['patient_id'].unique()
            if len(patients_with_label) <= 2:  # 如果某个标签只存在于2个或更少患者中
                problematic_labels.append((label, len(patients_with_label), patients_with_label))
        
        if problematic_labels:
            print(f"发现问题标签 (患者数量<=2): {problematic_labels}")
            print("由于标签分布不均，将使用样本级分层分割而不是患者级分割")
            return _split_stratified_simple(df, train_ratio, val_ratio, test_ratio, col_label)
        else:
            print("标签分布适合患者级分割")
            return _split_by_patient(df, train_ratio, val_ratio, test_ratio, col_label)
    else:
        return _split_stratified_simple(df, train_ratio, val_ratio, test_ratio, col_label)


def _split_by_patient(df, train_ratio, val_ratio, test_ratio, col_label):
    """
    基于患者ID进行分层分割，确保同一患者的数据不会跨集合
    同时确保所有数据集包含相同的标签集合
    """
    # 获取所有唯一标签
    all_labels = df[col_label].unique()
    print(f"数据中包含的所有标签: {sorted(all_labels)}")
    
    # 获取每个患者的主要标签（出现最多的标签）
    patient_labels = df.groupby('patient_id')[col_label].agg(lambda x: x.mode().iloc[0]).reset_index()
    
    # 检查每个标签对应的患者数量
    label_patient_counts = patient_labels[col_label].value_counts()
    print(f"每个标签对应的患者数量: {label_patient_counts.to_dict()}")
    
    # 初始化患者列表
    train_patients, val_patients, test_patients = [], [], []
    
    # 对于只有很少患者的标签，采用特殊处理
    rare_label_threshold = 3  # 如果某个标签的患者数少于3，采用特殊处理
    
    for label in all_labels:
        label_patients = patient_labels[patient_labels[col_label] == label]['patient_id'].tolist()
        n_patients = len(label_patients)
        
        print(f"标签 {label}: {n_patients} 个患者")
        
        if n_patients == 1:
            # 只有一个患者的标签：放入训练集，但同时在验证集和测试集中添加该患者的少量数据
            train_patients.extend(label_patients)
            print(f"  标签 {label} 只有1个患者，全部分配给训练集")
            
        elif n_patients == 2:
            # 两个患者的标签：一个给训练集，一个给测试集
            train_patients.append(label_patients[0])
            test_patients.append(label_patients[1])
            print(f"  标签 {label} 有2个患者，分别分配给训练集和测试集")
            
        elif n_patients < rare_label_threshold:
            # 少数患者的标签：大部分给训练集，确保其他集合也有
            train_patients.extend(label_patients[:max(1, int(n_patients * 0.7))])
            remaining = label_patients[max(1, int(n_patients * 0.7)):]
            if remaining:
                test_patients.extend(remaining[:len(remaining)//2 + len(remaining)%2])
                if len(remaining) > 1:
                    val_patients.extend(remaining[len(remaining)//2 + len(remaining)%2:])
            print(f"  标签 {label} 患者较少，主要分配给训练集")
            
        else:
            # 患者数量充足的标签：正常分层分割
            temp_train, temp_test = train_test_split(
                label_patients, 
                test_size=(val_ratio + test_ratio), 
                random_state=42,
                shuffle=True
            )
            
            if val_ratio > 0 and len(temp_test) > 1:
                temp_val, temp_test = train_test_split(
                    temp_test,
                    test_size=test_ratio/(val_ratio + test_ratio),
                    random_state=42,
                    shuffle=True
                )
                val_patients.extend(temp_val)
            
            train_patients.extend(temp_train)
            test_patients.extend(temp_test)
            print(f"  标签 {label} 正常分层分割完成")
    
    # 根据患者ID分割数据
    train_data = df[df['patient_id'].isin(train_patients)]
    val_data = df[df['patient_id'].isin(val_patients)]
    test_data = df[df['patient_id'].isin(test_patients)]
    
    # 检查分割后每个数据集的标签分布
    print("\n=== 分割后标签分布检查 ===")
    train_labels = set(train_data[col_label].unique())
    val_labels = set(val_data[col_label].unique()) if len(val_data) > 0 else set()
    test_labels = set(test_data[col_label].unique())
    
    print(f"训练集标签: {sorted(train_labels)}")
    print(f"验证集标签: {sorted(val_labels)}")
    print(f"测试集标签: {sorted(test_labels)}")
    
    # 处理标签不完整的情况
    missing_in_val = train_labels - val_labels if len(val_data) > 0 else set()
    missing_in_test = train_labels - test_labels
    
    if missing_in_val or missing_in_test:
        print(f"\n警告: 某些标签在分割后缺失")
        if missing_in_val:
            print(f"验证集缺失标签: {sorted(missing_in_val)}")
        if missing_in_test:
            print(f"测试集缺失标签: {sorted(missing_in_test)}")
        
        # 从训练集中移动少量数据到验证集和测试集以确保标签完整性
        if missing_in_val and len(val_data) > 0:
            for missing_label in missing_in_val:
                # 从训练集中找到该标签的数据
                missing_data = train_data[train_data[col_label] == missing_label]
                if len(missing_data) > 1:
                    # 移动一小部分数据到验证集
                    move_count = min(2, len(missing_data) // 4)
                    if move_count > 0:
                        indices_to_move = missing_data.index[:move_count]
                        val_data = pd.concat([val_data, train_data.loc[indices_to_move]])
                        train_data = train_data.drop(indices_to_move)
                        print(f"  从训练集移动 {move_count} 个标签{missing_label}的样本到验证集")
        
        if missing_in_test:
            for missing_label in missing_in_test:
                # 从训练集中找到该标签的数据
                missing_data = train_data[train_data[col_label] == missing_label]
                if len(missing_data) > 1:
                    # 移动一小部分数据到测试集
                    move_count = min(2, len(missing_data) // 4)
                    if move_count > 0:
                        indices_to_move = missing_data.index[:move_count]
                        test_data = pd.concat([test_data, train_data.loc[indices_to_move]])
                        train_data = train_data.drop(indices_to_move)
                        print(f"  从训练集移动 {move_count} 个标签{missing_label}的样本到测试集")
    
    # 最终检查
    print("\n=== 最终标签分布 ===")
    final_train_labels = sorted(train_data[col_label].unique())
    final_val_labels = sorted(val_data[col_label].unique()) if len(val_data) > 0 else []
    final_test_labels = sorted(test_data[col_label].unique())
    
    print(f"训练集标签: {final_train_labels}")
    print(f"验证集标签: {final_val_labels}")
    print(f"测试集标签: {final_test_labels}")
    
    # 分离特征和标签
    feature_cols = [col for col in df.columns if col != col_label]
    
    X_train = train_data[feature_cols]
    y_train = train_data[[col_label]]
    
    X_val = val_data[feature_cols] if len(val_data) > 0 else pd.DataFrame(columns=feature_cols)
    y_val = val_data[[col_label]] if len(val_data) > 0 else pd.DataFrame(columns=[col_label])
    
    X_test = test_data[feature_cols]
    y_test = test_data[[col_label]]
    
    return X_train, y_train, X_val, y_val, X_test, y_test


def _split_stratified_simple(df, train_ratio, val_ratio, test_ratio, col_label):
    """
    简单的分层分割（不考虑患者分组）
    对稀有标签进行特殊处理，确保所有数据集都包含所有标签
    """
    # 分离特征和标签
    feature_cols = [col for col in df.columns if col != col_label]
    X = df[feature_cols]
    y = df[col_label]
    
    print(f"样本级分层分割: {len(df)} 个样本")
    
    # 分析标签分布
    label_counts = y.value_counts()
    rare_labels = label_counts[label_counts < 10].index  # 少于10个样本的标签视为稀有
    
    if len(rare_labels) > 0:
        print(f"发现稀有标签 (样本数<10): {list(rare_labels)}")
        print("将对稀有标签进行特殊处理")
        
        # 分离稀有标签和常见标签的数据
        rare_mask = y.isin(rare_labels)
        rare_X = X[rare_mask]
        rare_y = y[rare_mask]
        common_X = X[~rare_mask]
        common_y = y[~rare_mask]
        
        # 对常见标签进行正常的分层分割
        if len(common_y) > 0:
            try:
                X_train_common, X_temp_common, y_train_common, y_temp_common = train_test_split(
                    common_X, common_y, 
                    test_size=(val_ratio + test_ratio),
                    stratify=common_y,
                    random_state=42
                )
                
                # 第二次分割：验证集 vs 测试集
                if val_ratio > 0:
                    X_val_common, X_test_common, y_val_common, y_test_common = train_test_split(
                        X_temp_common, y_temp_common,
                        test_size=test_ratio/(val_ratio + test_ratio),
                        stratify=y_temp_common,
                        random_state=42
                    )
                else:
                    X_val_common, y_val_common = pd.DataFrame(columns=feature_cols), pd.Series(dtype=y.dtype, name=col_label)
                    X_test_common, y_test_common = X_temp_common, y_temp_common
                    
            except ValueError as e:
                print(f"常见标签分层分割失败: {e}")
                # 如果分层分割失败，使用随机分割
                X_train_common, X_temp_common, y_train_common, y_temp_common = train_test_split(
                    common_X, common_y, 
                    test_size=(val_ratio + test_ratio),
                    random_state=42
                )
                
                if val_ratio > 0:
                    X_val_common, X_test_common, y_val_common, y_test_common = train_test_split(
                        X_temp_common, y_temp_common,
                        test_size=test_ratio/(val_ratio + test_ratio),
                        random_state=42
                    )
                else:
                    X_val_common, y_val_common = pd.DataFrame(columns=feature_cols), pd.Series(dtype=y.dtype, name=col_label)
                    X_test_common, y_test_common = X_temp_common, y_temp_common
        else:
            # 如果没有常见标签，初始化空的DataFrames
            X_train_common = X_val_common = X_test_common = pd.DataFrame(columns=feature_cols)
            y_train_common = y_val_common = y_test_common = pd.Series(dtype=y.dtype, name=col_label)
        
        # 对稀有标签进行特殊分配，确保每个数据集都有
        X_train_rare_list = []
        y_train_rare_list = []
        X_val_rare_list = []
        y_val_rare_list = []
        X_test_rare_list = []
        y_test_rare_list = []
        
        for rare_label in rare_labels:
            rare_label_data = df[df[col_label] == rare_label]
            rare_label_X = rare_label_data[feature_cols]
            rare_label_y = rare_label_data[col_label]
            
            n_samples = len(rare_label_data)
            print(f"  处理稀有标签 {rare_label}: {n_samples} 个样本")
            
            if n_samples == 1:
                # 只有一个样本：放入训练集，但复制到验证集和测试集
                X_train_rare_list.append(rare_label_X)
                y_train_rare_list.append(rare_label_y)
                if val_ratio > 0:
                    X_val_rare_list.append(rare_label_X.iloc[:1])  # 复制一份到验证集
                    y_val_rare_list.append(rare_label_y.iloc[:1])
                X_test_rare_list.append(rare_label_X.iloc[:1])  # 复制一份到测试集
                y_test_rare_list.append(rare_label_y.iloc[:1])
                
            elif n_samples == 2:
                # 两个样本：一个给训练集，一个给测试集，验证集复制训练集的
                X_train_rare_list.append(rare_label_X.iloc[:1])
                y_train_rare_list.append(rare_label_y.iloc[:1])
                if val_ratio > 0:
                    X_val_rare_list.append(rare_label_X.iloc[:1])  # 复制训练集的
                    y_val_rare_list.append(rare_label_y.iloc[:1])
                X_test_rare_list.append(rare_label_X.iloc[1:2])
                y_test_rare_list.append(rare_label_y.iloc[1:2])
                
            else:
                # 多个样本：至少给每个数据集分配一个
                indices = list(range(n_samples))
                np.random.seed(42)
                np.random.shuffle(indices)
                
                # 确保每个集合至少有一个样本
                train_indices = [indices[0]]  # 至少一个给训练集
                val_indices = [indices[1]] if val_ratio > 0 and len(indices) > 1 else []
                test_indices = [indices[2 if val_ratio > 0 else 1]] if len(indices) > (2 if val_ratio > 0 else 1) else [indices[-1]]
                
                # 分配剩余的样本
                remaining_indices = indices[3 if val_ratio > 0 else 2:]
                for i, idx in enumerate(remaining_indices):
                    if i % 3 == 0:
                        train_indices.append(idx)
                    elif i % 3 == 1 and val_ratio > 0:
                        val_indices.append(idx)
                    else:
                        test_indices.append(idx)
                
                X_train_rare_list.append(rare_label_X.iloc[train_indices])
                y_train_rare_list.append(rare_label_y.iloc[train_indices])
                if val_indices:
                    X_val_rare_list.append(rare_label_X.iloc[val_indices])
                    y_val_rare_list.append(rare_label_y.iloc[val_indices])
                X_test_rare_list.append(rare_label_X.iloc[test_indices])
                y_test_rare_list.append(rare_label_y.iloc[test_indices])
        
        # 合并常见标签和稀有标签的结果
        if X_train_rare_list:
            X_train_rare = pd.concat(X_train_rare_list, ignore_index=True)
            y_train_rare = pd.concat(y_train_rare_list, ignore_index=True)
            X_train = pd.concat([X_train_common, X_train_rare], ignore_index=True)
            y_train = pd.concat([y_train_common, y_train_rare], ignore_index=True)
        else:
            X_train, y_train = X_train_common, y_train_common
            
        if X_val_rare_list:
            X_val_rare = pd.concat(X_val_rare_list, ignore_index=True)
            y_val_rare = pd.concat(y_val_rare_list, ignore_index=True)
            X_val = pd.concat([X_val_common, X_val_rare], ignore_index=True)
            y_val = pd.concat([y_val_common, y_val_rare], ignore_index=True)
        else:
            X_val, y_val = X_val_common, y_val_common
            
        if X_test_rare_list:
            X_test_rare = pd.concat(X_test_rare_list, ignore_index=True)
            y_test_rare = pd.concat(y_test_rare_list, ignore_index=True)
            X_test = pd.concat([X_test_common, X_test_rare], ignore_index=True)
            y_test = pd.concat([y_test_common, y_test_rare], ignore_index=True)
        else:
            X_test, y_test = X_test_common, y_test_common
            
    else:
        # 没有稀有标签，使用标准的分层分割
        try:
            X_train, X_temp, y_train, y_temp = train_test_split(
                X, y, 
                test_size=(val_ratio + test_ratio),
                stratify=y,
                random_state=42
            )
            
            # 第二次分割：分离验证集和测试集
            if val_ratio > 0:
                X_val, X_test, y_val, y_test = train_test_split(
                    X_temp, y_temp,
                    test_size=test_ratio/(val_ratio + test_ratio),
                    stratify=y_temp,
                    random_state=42
                )
            else:
                X_val, y_val = pd.DataFrame(columns=feature_cols), pd.Series(dtype=y.dtype, name=col_label)
                X_test, y_test = X_temp, y_temp
                
        except ValueError as e:
            print(f"标准分层分割失败: {e}, 使用随机分割")
            X_train, X_temp, y_train, y_temp = train_test_split(
                X, y, 
                test_size=(val_ratio + test_ratio),
                random_state=42
            )
            
            if val_ratio > 0:
                X_val, X_test, y_val, y_test = train_test_split(
                    X_temp, y_temp,
                    test_size=test_ratio/(val_ratio + test_ratio),
                    random_state=42
                )
            else:
                X_val, y_val = pd.DataFrame(columns=feature_cols), pd.Series(dtype=y.dtype, name=col_label)
                X_test, y_test = X_temp, y_temp
    
    # 转换为DataFrame格式
    y_train = pd.DataFrame(y_train)
    y_val = pd.DataFrame(y_val)
    y_test = pd.DataFrame(y_test)
    
    # 最终验证
    print(f"\n=== 样本级分割结果 ===")
    print(f"训练集: {len(X_train)} 样本, 标签: {sorted(y_train[col_label].unique())}")
    print(f"验证集: {len(X_val)} 样本, 标签: {sorted(y_val[col_label].unique()) if len(y_val) > 0 else []}")
    print(f"测试集: {len(X_test)} 样本, 标签: {sorted(y_test[col_label].unique())}")
    
    return X_train, y_train, X_val, y_val, X_test, y_test


def print_split_info(X_train, y_train, X_val, y_val, X_test, y_test, col_label="label"):
    """
    打印数据分割信息
    """
    print("=== 数据分割信息 ===")
    print(f"训练集: {len(X_train)} 样本")
    print(f"验证集: {len(X_val)} 样本")
    print(f"测试集: {len(X_test)} 样本")
    print(f"总计: {len(X_train) + len(X_val) + len(X_test)} 样本")
    
    print("\n=== 标签分布 ===")
    if len(y_train) > 0:
        print("训练集标签分布:")
        print(y_train[col_label].value_counts().sort_index())
    
    if len(y_val) > 0:
        print("验证集标签分布:")
        print(y_val[col_label].value_counts().sort_index())
    
    if len(y_test) > 0:
        print("测试集标签分布:")
        print(y_test[col_label].value_counts().sort_index())
