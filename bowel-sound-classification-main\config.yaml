# Paths
# 如果使用原始数据 + 预处理
dataset_csv: "D:/PhD/Code/Python/work/bowel-sound-classification-main/segments_labels.csv"
audio_dir: "D:/PhD/Code/Python/work/bowel-sound-classification-main/processed_data"

# 或者直接指向28595741目录（需要额外处理）
raw_data_dir: "D:/PhD/Code/Python/work/bowel-sound-classification-main/28595741"

# Data split ratios
train_ratio: 0.7
val_ratio: 0.15
test_ratio: 0.15

# Model type
model_type: "wav2vec2"  # Choose from ['vgg', 'resnet', 'alexnet', 'cnn-lstm', 'svm', 'xgboost', 'knn', 'dtc', 'catboost', 'wav2vec2']

# Feature extraction type
feature_type: "original"  # Choose from ['log-mel', 'spectrogram', 'mfcc', 'original'] - Wav2Vec2需要原始音频

# Training parameters
learning_rate: 0.00002  # Wav2Vec2推荐较小的学习率(2e-5)
batch_size: 8  # Wav2Vec2推荐较小的batch size，显存占用较大
num_epochs: 10  # Wav2Vec2收敛较快，10个epoch通常足够

# Machine learning model settings
ml_feature_name: "compare"  # Choose between 'gemaps' or 'compare'
ml_epochs: 5

# Output paths
output_dir: "./results"
model_save_dir: "./saved_model"

