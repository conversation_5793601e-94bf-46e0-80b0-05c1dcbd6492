#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CUDA和GPU信息检测工具
检测PyTorch CUDA支持、GPU硬件信息等
可在任何项目中复用
"""

import sys
import platform
from datetime import datetime

def print_separator(title="", length=60):
    """打印分隔线"""
    if title:
        print(f"\n{'='*5} {title} {'='*(length-len(title)-12)}")
    else:
        print("="*length)

def check_pytorch():
    """检查PyTorch安装情况"""
    try:
        import torch
        return torch
    except ImportError:
        print("❌ PyTorch未安装！")
        print("安装命令:")
        print("  CPU版本: pip install torch torchvision torchaudio")
        print("  GPU版本: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return None

def check_system_info():
    """检查系统信息"""
    print_separator("系统信息")
    print(f"🖥️  操作系统: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📅 检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def check_cuda_installation():
    """检查CUDA安装"""
    print_separator("CUDA安装检查")
    
    # 检查nvidia-smi
    import subprocess
    try:
        result = subprocess.run(['nvidia-smi'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA驱动已安装")
            # 提取驱动版本
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Driver Version:' in line:
                    driver_version = line.split('Driver Version:')[1].split()[0]
                    print(f"🔧 驱动版本: {driver_version}")
                    break
        else:
            print("❌ NVIDIA驱动未正确安装")
    except FileNotFoundError:
        print("❌ nvidia-smi命令未找到，NVIDIA驱动可能未安装")
    except subprocess.TimeoutExpired:
        print("⚠️  nvidia-smi命令超时")
    except Exception as e:
        print(f"⚠️  检查NVIDIA驱动时出错: {e}")
    
    # 检查nvcc
    try:
        result = subprocess.run(['nvcc', '--version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        if result.returncode == 0:
            # 提取CUDA版本
            output = result.stdout
            for line in output.split('\n'):
                if 'release' in line and 'V' in line:
                    cuda_version = line.split('release')[1].split(',')[0].strip()
                    print(f"🎯 CUDA Toolkit版本: {cuda_version}")
                    break
            print("✅ CUDA Toolkit已安装")
        else:
            print("❌ CUDA Toolkit未安装或nvcc不在PATH中")
    except FileNotFoundError:
        print("❌ nvcc命令未找到，CUDA Toolkit可能未安装")
    except Exception as e:
        print(f"⚠️  检查CUDA Toolkit时出错: {e}")

def check_pytorch_cuda(torch):
    """检查PyTorch CUDA支持"""
    print_separator("PyTorch CUDA支持")
    
    print(f"🔥 PyTorch版本: {torch.__version__}")
    
    # CUDA编译支持
    cuda_version = torch.version.cuda
    if cuda_version:
        print(f"🎯 PyTorch编译的CUDA版本: {cuda_version}")
    else:
        print("❌ PyTorch未编译CUDA支持（CPU版本）")
        return False
    
    # CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"🚀 CUDA是否可用: {'✅ 可用' if cuda_available else '❌ 不可用'}")
    
    if not cuda_available:
        print("\n❌ CUDA不可用的可能原因:")
        print("  1. 安装了CPU版本的PyTorch")
        print("  2. PyTorch CUDA版本与系统CUDA版本不匹配")
        print("  3. NVIDIA驱动版本过旧")
        print("  4. CUDA Toolkit未正确安装")
        
        print("\n🔧 解决方案:")
        print("  重新安装GPU版本PyTorch:")
        print("  pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return False
    
    return True

def check_gpu_details(torch):
    """检查GPU详细信息"""
    if not torch.cuda.is_available():
        return
    
    print_separator("GPU详细信息")
    
    # GPU数量
    device_count = torch.cuda.device_count()
    print(f"🎮 GPU数量: {device_count}")
    
    # 遍历每个GPU
    for i in range(device_count):
        print(f"\n--- GPU {i} ---")
        
        # 基本信息
        device_name = torch.cuda.get_device_name(i)
        print(f"📱 设备名称: {device_name}")
        
        # 计算能力
        capability = torch.cuda.get_device_capability(i)
        print(f"⚡ CUDA算力: {capability[0]}.{capability[1]}")
        
        # 内存信息
        properties = torch.cuda.get_device_properties(i)
        total_memory = properties.total_memory / 1024**3
        print(f"💾 总显存: {total_memory:.1f} GB")
        
        # 当前内存使用
        if i == torch.cuda.current_device():
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            cached = torch.cuda.memory_reserved(i) / 1024**3
            usage_percent = torch.cuda.memory_allocated(i) / properties.total_memory * 100
            
            print(f"📊 已分配显存: {allocated:.2f} GB")
            print(f"📊 缓存显存: {cached:.2f} GB")
            print(f"📊 显存使用率: {usage_percent:.1f}%")
        
        # 特性支持
        print(f"🔧 多处理器数量: {properties.multi_processor_count}")
        
        # TensorCore支持（计算能力7.0+）
        tensor_core_support = capability[0] >= 7
        print(f"🚀 TensorCore支持: {'✅ 支持' if tensor_core_support else '❌ 不支持'}")
        
        # BF16支持检查（需要计算能力8.0+）
        try:
            bf16_support = torch.cuda.is_bf16_supported()
            print(f"🎯 BF16数字格式: {'✅ 支持' if bf16_support else '❌ 不支持'}")
        except:
            bf16_support = capability[0] >= 8
            print(f"🎯 BF16数字格式: {'✅ 支持' if bf16_support else '❌ 不支持'}")

def performance_test(torch):
    """简单性能测试"""
    if not torch.cuda.is_available():
        return
    
    print_separator("简单性能测试")
    
    try:
        import time
        
        # 创建测试张量
        size = 5000
        print(f"🧪 测试矩阵大小: {size}x{size}")
        
        # CPU测试
        start_time = time.time()
        a_cpu = torch.randn(size, size)
        b_cpu = torch.randn(size, size)
        c_cpu = torch.mm(a_cpu, b_cpu)
        cpu_time = time.time() - start_time
        print(f"⏱️  CPU计算时间: {cpu_time:.3f}秒")
        
        # GPU测试
        start_time = time.time()
        a_gpu = torch.randn(size, size).cuda()
        b_gpu = torch.randn(size, size).cuda()
        torch.cuda.synchronize()  # 确保GPU操作完成
        
        start_compute = time.time()
        c_gpu = torch.mm(a_gpu, b_gpu)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_compute
        total_gpu_time = time.time() - start_time
        
        print(f"⏱️  GPU计算时间: {gpu_time:.3f}秒")
        print(f"⏱️  GPU总时间(含传输): {total_gpu_time:.3f}秒")
        
        if gpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"🚀 GPU加速比: {speedup:.1f}x")
        
    except Exception as e:
        print(f"⚠️  性能测试失败: {e}")

def recommendations(torch):
    """给出建议"""
    print_separator("优化建议")
    
    if not torch.cuda.is_available():
        print("🔧 安装GPU版本PyTorch以获得更好性能")
        return
    
    device_count = torch.cuda.device_count()
    
    if device_count > 1:
        print("🎮 检测到多GPU，可以使用DataParallel或DistributedDataParallel")
    
    # 根据GPU算力给建议
    capability = torch.cuda.get_device_capability(0)
    if capability[0] >= 8:
        print("🚀 您的GPU支持最新特性，建议使用混合精度训练")
    elif capability[0] >= 7:
        print("⚡ 您的GPU支持TensorCore，建议在深度学习中启用")
    
    # 显存建议
    properties = torch.cuda.get_device_properties(0)
    total_memory = properties.total_memory / 1024**3
    
    if total_memory >= 24:
        print("💾 显存充足，可以使用较大的batch size")
    elif total_memory >= 8:
        print("💾 显存适中，注意优化batch size和模型大小")
    else:
        print("💾 显存较小，建议使用梯度累积或模型并行")

def main():
    """主函数"""
    print("🔍 CUDA和GPU环境检测工具")
    print_separator()
    
    # 系统信息
    check_system_info()
    
    # CUDA安装检查
    check_cuda_installation()
    
    # PyTorch检查
    torch = check_pytorch()
    if torch is None:
        return
    
    # PyTorch CUDA支持
    cuda_available = check_pytorch_cuda(torch)
    
    # GPU详细信息
    if cuda_available:
        check_gpu_details(torch)
        performance_test(torch)
    
    # 建议
    recommendations(torch)
    
    print_separator()
    print("🎉 检测完成！")

if __name__ == "__main__":
    main() 