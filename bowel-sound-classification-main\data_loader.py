from Straified_split import split_stratified
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import DataLoader, Dataset
import torch
import torchaudio
import torchaudio.transforms as T
import pandas as pd
from pathlib import Path
from torch.utils.data import Dataset

class PIUS(Dataset):
    def __init__(self, csv_file, audio_dir=".", feature='mel', transform=None, target_length=2000, use_log_mel=False):
        self.data = pd.read_csv(csv_file)
        self.audio_dir = Path(audio_dir)  # 添加这行
        self.data_type = feature.lower()
        self.transform = transform
        self.target_length = target_length
        self.use_log_mel = use_log_mel

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        wave_path = self.audio_dir / self.data.iloc[idx]['path']  # 拼接完整路径
        label = self.data.iloc[idx]['label']
        patient_id = self.data.iloc[idx]['patient_id']

        waveform, sample_rate = torchaudio.load(wave_path)
        waveform = self._pad_or_truncate(waveform)

        if self.data_type == 'original':
            data = waveform  # Return raw waveform instead of spectrogram
        else:
            data = self._extract_features(waveform, sample_rate, wave_path)

        if self.transform:
            data = self.transform(data)

        return data, label, patient_id

    def _pad_or_truncate(self, waveform):
        if waveform.shape[1] < self.target_length:
            padding = self.target_length - waveform.shape[1]
            return torch.nn.functional.pad(waveform, (0, padding), "constant", 0)
        return waveform[:, :self.target_length]

    def _extract_features(self, waveform, sample_rate, wave_path):
        if self.data_type == 'mel':
            spectrogram = T.MelSpectrogram(
                sample_rate=sample_rate, n_fft=512, hop_length=256, n_mels=32
            )(waveform)
            return torch.log1p(spectrogram) if self.use_log_mel else spectrogram
        elif self.data_type == 'mfcc':
            return T.MFCC(sample_rate=sample_rate, n_mfcc=13, melkwargs={"n_fft": 512, "n_mels": 32})(waveform)
        elif self.data_type == 'spectrogram':
            return T.Spectrogram()(waveform)
        elif self.data_type == 'log-mel':
            spectrogram = T.MelSpectrogram(
                sample_rate=sample_rate, n_fft=512, hop_length=256, n_mels=32
            )(waveform)
            return torch.log1p(spectrogram)
        else:
            raise ValueError(f"Invalid data type: {self.data_type}")



def load_and_split_data(csv_file, subset_ratios, filename="subset.txt"):
    df = pd.read_csv(csv_file)
    
    # 先保存原始标签
    original_labels = df['label'].unique()
    print(f"原始标签: {sorted(original_labels)}")
    
    # 对所有标签进行编码
    le = LabelEncoder()
    df['label'] = le.fit_transform(df['label'])
    
    # 打印标签映射关系
    label_mapping = dict(zip(le.classes_, le.transform(le.classes_)))
    print(f"标签编码映射: {label_mapping}")
    
    # 重命名列（如果需要）
    if 'New_Wav_File' in df.columns:
        df.rename(columns={'New_Wav_File': 'path'}, inplace=True)

    # 执行分层分割
    X_train, y_train, X_val, y_val, X_test, y_test = split_stratified(
        df, subset_ratios=subset_ratios, filename=filename, col_subset="subset", col_index=None, col_label="label"
    )

    # 合并特征和标签
    train_data, val_data, test_data = [
        pd.concat([X, y], axis=1) for X, y in [(X_train, y_train), (X_val, y_val), (X_test, y_test)]
    ]

    # 验证分割后的标签一致性
    print(f"\n=== 数据分割后标签验证 ===")
    train_labels = sorted(train_data['label'].unique())
    val_labels = sorted(val_data['label'].unique()) if len(val_data) > 0 else []
    test_labels = sorted(test_data['label'].unique())
    
    print(f"训练集编码后标签: {train_labels}")
    print(f"验证集编码后标签: {val_labels}")
    print(f"测试集编码后标签: {test_labels}")
    
    # 检查标签一致性
    all_encoded_labels = set(range(len(le.classes_)))
    train_set = set(train_labels)
    val_set = set(val_labels) if val_labels else set()
    test_set = set(test_labels)
    
    missing_in_train = all_encoded_labels - train_set
    missing_in_val = all_encoded_labels - val_set if val_labels else set()
    missing_in_test = all_encoded_labels - test_set
    
    if missing_in_train or missing_in_val or missing_in_test:
        print(f"警告: 某些编码标签在分割后缺失!")
        if missing_in_train:
            print(f"  训练集缺失标签: {sorted(missing_in_train)}")
        if missing_in_val:
            print(f"  验证集缺失标签: {sorted(missing_in_val)}")
        if missing_in_test:
            print(f"  测试集缺失标签: {sorted(missing_in_test)}")
    else:
        print("✓ 所有数据集都包含完整的标签集合")

    # 保存分割后的数据
    for data, name in zip([train_data, val_data, test_data], ['train', 'val', 'test']):
        # 检查是否有patient_id列，如果有就包含进去
        cols_to_save = ['path', 'label']
        if 'patient_id' in data.columns:
            cols_to_save.append('patient_id')
        data[cols_to_save].to_csv(f'{name}_data.csv', index=False)

    return 'train_data.csv', 'val_data.csv', 'test_data.csv', le


def get_dataloaders(train_csv, val_csv, test_csv, audio_dir, feature, transform, batch_size=16):
    import platform
    
    datasets = {
        'train': PIUS(train_csv, audio_dir=audio_dir, feature=feature, transform=transform, target_length=2000),
        'val': PIUS(val_csv, audio_dir=audio_dir, feature=feature, transform=transform, target_length=2000),
        'test': PIUS(test_csv, audio_dir=audio_dir, feature=feature, transform=transform, target_length=2000)
    }
    
    # 在Windows上使用num_workers=0避免multiprocessing问题
    num_workers = 0 if platform.system() == 'Windows' else 4
    
    return {k: DataLoader(
        v, 
        batch_size=batch_size, 
        shuffle=(k == 'train'),
        num_workers=num_workers,     # Windows上设为0，其他系统设为4
        pin_memory=True,             # 内存固定，加速CPU到GPU的数据传输
        persistent_workers=False,    # Windows上关闭，避免worker进程问题
        drop_last=False              # 保留最后一个不完整的batch
    ) for k, v in datasets.items()}