#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动下载 Wav2Vec2 预训练模型
解决网络连接问题
"""

import os
import requests
from pathlib import Path
import json

def download_file(url, local_path):
    """下载文件"""
    print(f"下载: {url}")
    print(f"保存到: {local_path}")
    
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"✅ 下载完成: {local_path}")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def download_wav2vec2_model():
    """下载 Wav2Vec2 模型文件"""
    model_dir = "./wav2vec2-base"
    
    # 创建模型目录
    os.makedirs(model_dir, exist_ok=True)
    
    # 模型文件列表
    files_to_download = [
        ("config.json", "https://huggingface.co/facebook/wav2vec2-base/raw/main/config.json"),
        ("pytorch_model.bin", "https://huggingface.co/facebook/wav2vec2-base/resolve/main/pytorch_model.bin"),
        ("feature_extractor_config.json", "https://huggingface.co/facebook/wav2vec2-base/raw/main/preprocessor_config.json")
    ]
    
    print("🚀 开始下载 Wav2Vec2 预训练模型...")
    
    success_count = 0
    for filename, url in files_to_download:
        local_path = os.path.join(model_dir, filename)
        
        # 如果文件已存在，跳过下载
        if os.path.exists(local_path):
            print(f"⏭️  文件已存在，跳过: {filename}")
            success_count += 1
            continue
            
        if download_file(url, local_path):
            success_count += 1
        else:
            print(f"❌ 无法下载: {filename}")
    
    print(f"\n📊 下载结果: {success_count}/{len(files_to_download)} 个文件成功")
    
    if success_count == len(files_to_download):
        print("🎉 所有文件下载完成！")
        return True
    else:
        print("⚠️  部分文件下载失败，请检查网络连接")
        return False

if __name__ == "__main__":
    success = download_wav2vec2_model()
    if success:
        print("\n✅ 可以继续运行 Wav2Vec2 训练了！")
    else:
        print("\n❌ 请检查网络连接或尝试使用镜像源") 