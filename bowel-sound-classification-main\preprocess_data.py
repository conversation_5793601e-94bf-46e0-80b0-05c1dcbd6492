"""
数据预处理脚本
将原始音频文件按时间标注分割成小段，并生成CSV标签文件
"""

import os
import pandas as pd
import torchaudio
import numpy as np
from pathlib import Path
import argparse


def load_annotations(txt_file):
    """
    加载时间标注文件
    
    参数:
        txt_file: 标注文件路径
    
    返回:
        annotations: 包含开始时间、结束时间和标签的列表
    """
    annotations = []
    
    with open(txt_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                parts = line.split('\t')
                if len(parts) >= 3:
                    start_time = float(parts[0])
                    end_time = float(parts[1])
                    label = parts[2].strip()
                    annotations.append({
                        'start_time': start_time,
                        'end_time': end_time,
                        'label': label
                    })
    
    return annotations


def extract_patient_id(filename):
    """
    从文件名提取患者ID
    例如: record_080524001_1.wav -> 080524001
    """
    # 移除扩展名
    name_without_ext = os.path.splitext(filename)[0]
    # 分割并提取患者ID部分
    parts = name_without_ext.split('_')
    if len(parts) >= 2:
        return parts[1]  # 080524001
    return name_without_ext


def segment_audio(audio_file, annotations, output_dir, min_duration=0.5, max_duration=10.0):
    """
    根据标注分割音频文件
    
    参数:
        audio_file: 原始音频文件路径
        annotations: 时间标注列表
        output_dir: 输出目录
        min_duration: 最小段长度（秒）
        max_duration: 最大段长度（秒）
    
    返回:
        segments_info: 分割后的段信息列表
    """
    # 加载音频
    waveform, sample_rate = torchaudio.load(audio_file)
    
    # 获取文件名（不含扩展名）
    base_name = os.path.splitext(os.path.basename(audio_file))[0]
    patient_id = extract_patient_id(os.path.basename(audio_file))
    
    segments_info = []
    
    for i, annotation in enumerate(annotations):
        start_time = annotation['start_time']
        end_time = annotation['end_time']
        label = annotation['label']
        
        # 计算段长度
        duration = end_time - start_time
        
        # 过滤太短或太长的段
        if duration < min_duration or duration > max_duration:
            print(f"跳过段 {i}: 长度 {duration:.2f}s (标签: {label})")
            continue
        
        # 转换时间到样本索引
        start_sample = int(start_time * sample_rate)
        end_sample = int(end_time * sample_rate)
        
        # 确保索引在有效范围内
        start_sample = max(0, start_sample)
        end_sample = min(waveform.shape[1], end_sample)
        
        if start_sample >= end_sample:
            continue
        
        # 提取音频段
        segment = waveform[:, start_sample:end_sample]
        
        # 生成输出文件名
        segment_filename = f"{base_name}_segment_{i:03d}.wav"
        segment_path = os.path.join(output_dir, segment_filename)
        
        # 保存音频段
        torchaudio.save(segment_path, segment, sample_rate)
        
        # 记录段信息
        segments_info.append({
            'path': segment_filename,
            'label': label,
            'patient_id': patient_id,
            'start_time': start_time,
            'end_time': end_time,
            'duration': duration,
            'original_file': os.path.basename(audio_file)
        })
        
        print(f"保存段 {i}: {segment_filename} (标签: {label}, 长度: {duration:.2f}s)")
    
    return segments_info


def add_silence_segments(audio_file, annotations, output_dir, silence_duration=2.0, 
                        min_gap=1.0, max_silence_segments=10):
    """
    添加静默段（NONE标签）
    
    参数:
        audio_file: 原始音频文件路径
        annotations: 时间标注列表
        output_dir: 输出目录
        silence_duration: 静默段长度（秒）
        min_gap: 最小间隙长度（秒）
        max_silence_segments: 最大静默段数量
    
    返回:
        silence_segments: 静默段信息列表
    """
    # 加载音频
    waveform, sample_rate = torchaudio.load(audio_file)
    audio_duration = waveform.shape[1] / sample_rate
    
    base_name = os.path.splitext(os.path.basename(audio_file))[0]
    patient_id = extract_patient_id(os.path.basename(audio_file))
    
    # 找到标注之间的间隙
    gaps = []
    sorted_annotations = sorted(annotations, key=lambda x: x['start_time'])
    
    # 开头的间隙
    if sorted_annotations and sorted_annotations[0]['start_time'] > min_gap:
        gaps.append((0, sorted_annotations[0]['start_time']))
    
    # 中间的间隙
    for i in range(len(sorted_annotations) - 1):
        gap_start = sorted_annotations[i]['end_time']
        gap_end = sorted_annotations[i + 1]['start_time']
        if gap_end - gap_start > min_gap:
            gaps.append((gap_start, gap_end))
    
    # 结尾的间隙
    if sorted_annotations and audio_duration - sorted_annotations[-1]['end_time'] > min_gap:
        gaps.append((sorted_annotations[-1]['end_time'], audio_duration))
    
    silence_segments = []
    silence_count = 0
    
    for gap_start, gap_end in gaps:
        if silence_count >= max_silence_segments:
            break
            
        gap_duration = gap_end - gap_start
        if gap_duration >= silence_duration:
            # 在间隙中间提取静默段
            silence_start = gap_start + (gap_duration - silence_duration) / 2
            silence_end = silence_start + silence_duration
            
            # 转换到样本索引
            start_sample = int(silence_start * sample_rate)
            end_sample = int(silence_end * sample_rate)
            
            # 提取静默段
            segment = waveform[:, start_sample:end_sample]
            
            # 生成文件名
            segment_filename = f"{base_name}_silence_{silence_count:03d}.wav"
            segment_path = os.path.join(output_dir, segment_filename)
            
            # 保存音频段
            torchaudio.save(segment_path, segment, sample_rate)
            
            silence_segments.append({
                'path': segment_filename,
                'label': 'NONE',
                'patient_id': patient_id,
                'start_time': silence_start,
                'end_time': silence_end,
                'duration': silence_duration,
                'original_file': os.path.basename(audio_file)
            })
            
            silence_count += 1
            print(f"保存静默段: {segment_filename} (时间: {silence_start:.2f}-{silence_end:.2f}s)")
    
    return silence_segments


def process_dataset(input_dir, output_dir, csv_output_path):
    """
    处理整个数据集
    
    参数:
        input_dir: 输入目录（包含原始音频和标注文件）
        output_dir: 输出目录（保存分割后的音频）
        csv_output_path: CSV标签文件输出路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    all_segments = []
    
    # 查找所有音频文件
    audio_files = list(Path(input_dir).glob("*.wav"))
    
    print(f"找到 {len(audio_files)} 个音频文件")
    
    for audio_file in audio_files:
        print(f"\n处理文件: {audio_file.name}")
        
        # 查找对应的标注文件
        txt_file = audio_file.with_suffix('.txt')
        
        if not txt_file.exists():
            print(f"警告: 找不到标注文件 {txt_file.name}")
            continue
        
        # 加载标注
        annotations = load_annotations(txt_file)
        print(f"加载了 {len(annotations)} 个标注")
        
        # 分割音频
        segments = segment_audio(str(audio_file), annotations, output_dir)
        all_segments.extend(segments)
        
        # 添加静默段
        silence_segments = add_silence_segments(str(audio_file), annotations, output_dir)
        all_segments.extend(silence_segments)
    
    # 创建CSV文件
    df = pd.DataFrame(all_segments)
    df.to_csv(csv_output_path, index=False)
    
    print(f"\n数据处理完成!")
    print(f"总共生成 {len(all_segments)} 个音频段")
    print(f"CSV文件保存到: {csv_output_path}")
    
    # 打印标签分布
    print("\n标签分布:")
    print(df['label'].value_counts())
    
    print("\n患者分布:")
    print(df['patient_id'].value_counts())


def main():
    parser = argparse.ArgumentParser(description='预处理肠鸣音数据')
    parser.add_argument('--input_dir', type=str, default='./28595741',
                       help='输入目录路径')
    parser.add_argument('--output_dir', type=str, default='./processed_audio',
                       help='输出目录路径')
    parser.add_argument('--csv_output', type=str, default='./segments_labels.csv',
                       help='CSV标签文件输出路径')
    
    args = parser.parse_args()
    
    # 处理数据集
    process_dataset(args.input_dir, args.output_dir, args.csv_output)


if __name__ == "__main__":
    main()
