{"best_metric": 0.6419653329398923, "best_model_checkpoint": "./results\\checkpoint-52", "epoch": 5.0, "eval_steps": 500, "global_step": 260, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.19230769230769232, "grad_norm": 9.258394241333008, "learning_rate": 4.0000000000000003e-07, "loss": 1.9321, "step": 10}, {"epoch": 0.38461538461538464, "grad_norm": 18.735471725463867, "learning_rate": 8.000000000000001e-07, "loss": 1.9234, "step": 20}, {"epoch": 0.5769230769230769, "grad_norm": 8.991275787353516, "learning_rate": 1.2000000000000002e-06, "loss": 1.8941, "step": 30}, {"epoch": 0.7692307692307693, "grad_norm": 3.130134344100952, "learning_rate": 1.6000000000000001e-06, "loss": 1.8603, "step": 40}, {"epoch": 0.9615384615384616, "grad_norm": 4.1993513107299805, "learning_rate": 2.0000000000000003e-06, "loss": 1.8043, "step": 50}, {"epoch": 1.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.4887640449438202, "eval_AUC_class_2_HS": 0.48863636363636365, "eval_AUC_class_3_MB": 0.5238095238095238, "eval_AUC_class_4_NONE": 0.4876543209876543, "eval_AUC_class_5_unsure_CRS": 0.9943820224719101, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.885746955871582, "eval_overall_AUC": 0.6419653329398923, "eval_runtime": 2.0611, "eval_samples_per_second": 43.665, "eval_steps_per_second": 5.822, "step": 52}, {"epoch": 1.1538461538461537, "grad_norm": 8.77101993560791, "learning_rate": 2.4000000000000003e-06, "loss": 1.7237, "step": 60}, {"epoch": 1.3461538461538463, "grad_norm": 2.760019063949585, "learning_rate": 2.8000000000000003e-06, "loss": 1.7271, "step": 70}, {"epoch": 1.5384615384615383, "grad_norm": 3.308788776397705, "learning_rate": 3.2000000000000003e-06, "loss": 1.6626, "step": 80}, {"epoch": 1.7307692307692308, "grad_norm": 4.3630852699279785, "learning_rate": 3.6000000000000003e-06, "loss": 1.543, "step": 90}, {"epoch": 1.9230769230769231, "grad_norm": 2.608245611190796, "learning_rate": 4.000000000000001e-06, "loss": 1.5455, "step": 100}, {"epoch": 2.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.4887640449438202, "eval_AUC_class_2_HS": 0.48863636363636365, "eval_AUC_class_3_MB": 0.5238095238095238, "eval_AUC_class_4_NONE": 0.5123456790123457, "eval_AUC_class_5_unsure_CRS": 0.5, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.7238798141479492, "eval_overall_AUC": 0.5748666665902897, "eval_runtime": 2.0631, "eval_samples_per_second": 43.624, "eval_steps_per_second": 5.817, "step": 104}, {"epoch": 2.1153846153846154, "grad_norm": 3.9611449241638184, "learning_rate": 4.4e-06, "loss": 1.4407, "step": 110}, {"epoch": 2.3076923076923075, "grad_norm": 3.153988838195801, "learning_rate": 4.800000000000001e-06, "loss": 1.4305, "step": 120}, {"epoch": 2.5, "grad_norm": 4.573875904083252, "learning_rate": 5.2e-06, "loss": 1.3452, "step": 130}, {"epoch": 2.6923076923076925, "grad_norm": 3.0615439414978027, "learning_rate": 5.600000000000001e-06, "loss": 1.4591, "step": 140}, {"epoch": 2.8846153846153846, "grad_norm": 3.278473138809204, "learning_rate": 6e-06, "loss": 1.325, "step": 150}, {"epoch": 3.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.4887640449438202, "eval_AUC_class_2_HS": 0.48863636363636365, "eval_AUC_class_3_MB": 0.5238095238095238, "eval_AUC_class_4_NONE": 0.4876543209876543, "eval_AUC_class_5_unsure_CRS": 0.9943820224719101, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.4032949209213257, "eval_overall_AUC": 0.6419653329398923, "eval_runtime": 2.1854, "eval_samples_per_second": 41.183, "eval_steps_per_second": 5.491, "step": 156}, {"epoch": 3.076923076923077, "grad_norm": 3.8030543327331543, "learning_rate": 6.4000000000000006e-06, "loss": 1.3344, "step": 160}, {"epoch": 3.269230769230769, "grad_norm": 3.5218520164489746, "learning_rate": 6.800000000000001e-06, "loss": 1.2967, "step": 170}, {"epoch": 3.4615384615384617, "grad_norm": 1.7426785230636597, "learning_rate": 7.2000000000000005e-06, "loss": 1.4061, "step": 180}, {"epoch": 3.6538461538461537, "grad_norm": 4.470120906829834, "learning_rate": 7.600000000000001e-06, "loss": 1.182, "step": 190}, {"epoch": 3.8461538461538463, "grad_norm": 5.201157569885254, "learning_rate": 8.000000000000001e-06, "loss": 1.2639, "step": 200}, {"epoch": 4.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.4887640449438202, "eval_AUC_class_2_HS": 0.48863636363636365, "eval_AUC_class_3_MB": 0.5238095238095238, "eval_AUC_class_4_NONE": 0.4876543209876543, "eval_AUC_class_5_unsure_CRS": 0.9943820224719101, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.2803332805633545, "eval_overall_AUC": 0.6419653329398923, "eval_runtime": 2.4297, "eval_samples_per_second": 37.042, "eval_steps_per_second": 4.939, "step": 208}, {"epoch": 4.038461538461538, "grad_norm": 1.763980507850647, "learning_rate": 8.400000000000001e-06, "loss": 1.1286, "step": 210}, {"epoch": 4.230769230769231, "grad_norm": 5.469284534454346, "learning_rate": 8.8e-06, "loss": 1.3911, "step": 220}, {"epoch": 4.423076923076923, "grad_norm": 3.5826525688171387, "learning_rate": 9.200000000000002e-06, "loss": 1.1375, "step": 230}, {"epoch": 4.615384615384615, "grad_norm": 2.901235580444336, "learning_rate": 9.600000000000001e-06, "loss": 1.1359, "step": 240}, {"epoch": 4.8076923076923075, "grad_norm": 2.567579746246338, "learning_rate": 1e-05, "loss": 1.1184, "step": 250}, {"epoch": 5.0, "grad_norm": 8.209166526794434, "learning_rate": 1.04e-05, "loss": 1.3837, "step": 260}, {"epoch": 5.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.5, "eval_AUC_class_2_HS": 0.5, "eval_AUC_class_3_MB": 0.5, "eval_AUC_class_4_NONE": 0.4876543209876543, "eval_AUC_class_5_unsure_CRS": 0.5, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.224780797958374, "eval_overall_AUC": 0.5711664822453756, "eval_runtime": 3.0504, "eval_samples_per_second": 29.504, "eval_steps_per_second": 3.934, "step": 260}], "logging_steps": 10, "max_steps": 260, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1.86114498e+16, "train_batch_size": 8, "trial_name": null, "trial_params": null}