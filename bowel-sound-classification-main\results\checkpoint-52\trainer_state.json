{"best_metric": 0.6419653329398923, "best_model_checkpoint": "./results\\checkpoint-52", "epoch": 1.0, "eval_steps": 500, "global_step": 52, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.19230769230769232, "grad_norm": 9.258394241333008, "learning_rate": 4.0000000000000003e-07, "loss": 1.9321, "step": 10}, {"epoch": 0.38461538461538464, "grad_norm": 18.735471725463867, "learning_rate": 8.000000000000001e-07, "loss": 1.9234, "step": 20}, {"epoch": 0.5769230769230769, "grad_norm": 8.991275787353516, "learning_rate": 1.2000000000000002e-06, "loss": 1.8941, "step": 30}, {"epoch": 0.7692307692307693, "grad_norm": 3.130134344100952, "learning_rate": 1.6000000000000001e-06, "loss": 1.8603, "step": 40}, {"epoch": 0.9615384615384616, "grad_norm": 4.1993513107299805, "learning_rate": 2.0000000000000003e-06, "loss": 1.8043, "step": 50}, {"epoch": 1.0, "eval_AUC_class_0_CRS": 0.5161290322580645, "eval_AUC_class_1_CS": 0.4887640449438202, "eval_AUC_class_2_HS": 0.48863636363636365, "eval_AUC_class_3_MB": 0.5238095238095238, "eval_AUC_class_4_NONE": 0.4876543209876543, "eval_AUC_class_5_unsure_CRS": 0.9943820224719101, "eval_AUC_class_6_unsure_MB": 0.9943820224719101, "eval_loss": 1.885746955871582, "eval_overall_AUC": 0.6419653329398923, "eval_runtime": 2.0611, "eval_samples_per_second": 43.665, "eval_steps_per_second": 5.822, "step": 52}], "logging_steps": 10, "max_steps": 260, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 3722289960000000.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}