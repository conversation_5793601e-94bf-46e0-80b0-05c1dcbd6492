# 学术GPT辅助工具集

本项目提供了一套用于增强学术GPT功能的工具集，包括论文翻译功能增强和PDF文件批量重命名工具。

## 功能概述

### 核心功能：

1. 项目自带学术gpt-3.80版
2. markdown翻译插持件续优化：对接了qwq32b等大模型，对接了mineru的ocr输出的md文件。实现批量翻译mineru识别出的md文件。
3. arxiv论文得翻译持续优化。

## 安装说明

### 1. 基础环境要求

ocr使用独立软件：   mineru：

是独立的ocr系统，不耗费本机资源，只需要下载安装就能ocr，

下载地址：https://mineru.net/

![1751892206859](image/README/1751892206859.png)

然后mineru输出的ocr的结果，通过本项目对接，进行后续的翻译工作。

下图为mineru的界面：

把你的pdf拖动进去就能进行ocr

最新版（202507）的mineru，有了一个新的引擎，速度快，可识别表格。

![1751892164124](image/README/1751892164124.png)

markdown批量翻译   只需要安装学术gpt

- Python 3.7+
- 学术GPT环境  （学术gpt辅助工具\markdown翻译对接mineru和qwq32b-使用ga380\gpt_academic这个目录下的requirements.txt  安装一下）
- latex环境  tex live（可选，翻译arxiv网站论文时才使用）

### 2. 依赖安装

批量翻译：
直接使用学术gpt的依赖和latex环境依赖即可，无需安装新的依赖。

## 前置安装：

## 学术gpt3.8版的安装说明

#### 1、下载本项目整个文件

![1751678079140](image/README/1751678079140.png)

![1751678113078](image/README/1751678113078.png)

或者使用git clone：

git clone https://gitee.com/shijunbao1/ga-assistant.git

#### 2、创建conda环境：

这里要选择使用conda环境或者本机环境来安装。推荐使用conda环境来安装：

（推荐：）

创建conda环境：

conda create -n assistant python=3.11 -y

激活conda环境：

conda activate assistant

如果选择本机 则跳过这两句。

#### 4、安装指定版本的gradio库。

实测可用的版本是gradio 3.32版

注意：不可以安装最新版gradio库。

pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ https://public.agent-matrix.com/publish/gradio-3.32.10-py3-none-any.whl

#### 3、安装reuirements.txt

目录位置通过cd命令  达到：学术gpt辅助工具\markdown翻译对接mineru和qwq32b-使用ga380\gpt_academic

这里是学术gpt整个项目在本项目的位置。

然后执行安装：

pip install -r requirements.txt

4、配置学术gpt

配置config.py

`config_private.py`   新建这个文件 ，复制配置信息

重要的配置信息有如下：  我提供示意值，需要用户改为自己的值。

重要参数（只列出对系统功能影响较大的核心项）：

1. API_KEY

* 你的 OpenAI/One-API 等模型的密钥，决定了能否正常调用大模型服务。
* 当前值：sk-vPd3q8Tknq1vILpSFa82Af27FcEd4e198c49Ce7e35F20a3f
* 说明： 推荐使用one api 这个开源项目来统一对接，管理所有在线，本地大模型。

1. LLM_MODEL

* 默认选中的大模型名称，必须包含在 AVAIL_LLM_MODELS 里。
* 当前值：one-api-deepseek-r1
* 特别说明：
* 使用了one api项目对接大模型，要有前缀 one-api-  比如：“one-api-qwen3-8b”
* 使用了vllm对接的大模型，要有前缀 vllm-   比如：“vllm-qwen3-8b”

1. AVAIL_LLM_MODELS

* 可用的大模型列表，决定了你可以切换和调用哪些模型。
* 按照你自己的模型名称去改。
* 特别说明：
* 使用了one api项目对接大模型，要有前缀 one-api-  比如：“one-api-qwen3-8b”
* 使用了vllm对接的大模型，要有前缀 vllm-   比如：“vllm-qwen3-8b”

1. API_URL_REDIRECT

* API 请求重定向，决定了你的请求会被转发到哪个后端服务。
* 当前值：{"https://api.openai.com/v1/chat/completions": "http://localhost:30001/v1/chat/completions"}
* 右边是你使用的服务器地址。 推荐本地安装oneapi来统一对接。
* 左边的地址不要动。

1. DEFAULT_WORKER_NUM

* 并发线程数，影响多线程插件的性能。
* 当前值：20
* 说明：本地双卡：10-15   本地四卡：20-30  （针对32B模型） 需要用户多次尝试才能确定。

1. INIT_SYS_PROMPT

* 系统初始提示词，影响 AI 助手的基础行为。
* 当前值：你是一个靠谱的AI助手.

1. CHATBOT_HEIGHT、LAYOUT、THEME、DARK_MODE

* 影响界面布局和主题风格。
* 笔者默认使用纵向布局。

1. USE_PROXY

* 是否使用代理访问外部服务。
* 当前值：False
* 对接国外模型需要打开。且服务器地址配置成代理服务器约定的地址。

1. WEB_PORT

* 网页服务端口。
* 当前值：55996
* 五位数不容易和其他软件端口产生冲突

1. AUTO_OPEN_BROWSER

* 是否自动打开浏览器。
* 当前值：True

1. TIMEOUT_SECONDS

* 请求超时时间。
* 当前值：300

1. MAX_RETRY

* 请求失败时的最大重试次数。
* 当前值：5

## 使用说明

### 1.使用qwq32b、qwenlong32b这种推理模型精准批量翻译mineru识别出来的pdf的markdown文件。

目前只推荐这两种模型，翻译效果最好，

目前（2025.07），我对比发现，qwenlong32b  翻译效果要比qwq32b好一些，中文语句更流畅丝滑。

流程图：

![1742228741120](image/README/1742228741120.png)

注意：

黄色为mineru，是独立的项目。

红色为本项目，即学术gpt项目辅助工具集-markdown翻译工具。

1、先使用mineru识别需要翻译的pdf文件

   ![1742017150265](image/README/1742017150265.png)

   2、本项目已经把学术gpt-3.80集成进来了，直接安装中科院学术gpt3.80（markdown翻译加强版）后，使用

![1750209600435](image/README/1750209600435.png)

主页的按钮插件区的第一个按钮，“Markdown翻译（指定翻译成何种语言）”

2.1  配置并发数量和上下文等常用参数：（主界面有专属的markdown配置面板）

![1753440985278](image/README/1753440985278.png)

主界面下方，有两个栏目，用于配置markdown的设置。

### 左边：

提示词。

用户可以自定义提示词，放在这里，每次点击markdown翻译按钮会优先使用这里输入的提示词。

这里清空后，使用系统自带的英译中v0.85版提示词。

如果你想使用中译英或者其他语种，请在这里写相应的要求。这里非空时，系统内部的提示词自动禁用，因为内部提示词是兜底策略，优先级低。

### 右边：

常用参数设置：

打开公式保护：  这个一般不要选，因为是实验性功能，只有常规方法效果不理想时候再选。 作用是保护公式，不被大模型翻译破坏。

最大token数： 本项目按照切片来多线程翻译，所以使用token数量来限制每个切片的待翻译原文的长度  一般设置为1000-2000。

最大并发数： 多线程翻译的最大并发线程数量。和大模型相关，一般为5-80个线程。本地一般设置为1-40个之间的一个数，得多次尝试才能确定。

大纲最大字符数：

提取整本书籍的大纲作为前置信息，用于翻译时的宏观校对和全局掌控。一般设置为1000-3000个字符。看用户的模型能力，上下文长的加多一些。

默认批处理目标文件夹：

用于保存一个固定的待翻译的工作目录，方便快速执行任务。不需要可清空。

主界面输入框输入的路径后，此路径配置不起作用。
只有主界面输入框为空时，此路径配置会生效。

### 以上右侧的所有参数  保存才能生效。

也可以这样：

gpt_academic目录的crazy_functions目录下，Markdown_Translate.py打开，编辑：

![1742017739223](image/README/1742017739223.png)

   3、运行学术gpt3.80

  4、新建一个文件夹 ，把mineru识别好的整个文件夹，可以是多个文件夹放进去

![1742017379812](image/README/1742017379812.png)

下图显示的就是mineru处理好的，待翻译的书籍文件夹，每本书是一个文件夹，多本书不要放到一个文件夹里。

程序自动识别每本书ocr后的full.md这个文件来进行翻译的。

  ![1742017434430](image/README/1742017434430.png)

5、复制这个待翻译的含有多本书的根目录的路径到学术gpt聊天输入框

![1742017538827](image/README/1742017538827.png)

6、界面最下方的prompt区域写入你的要求，   比如翻译的具体要求以及翻译成何种语言。

如果不写（空的情况下），会自动调用系统自带的提示词。

推荐使用本人研发的prompt。

prompt路径在项目的：.\markdown翻译对接mineru和qwq32b-使用ga380\辅助工具

这个目录下的 markdown翻译配套提示词xxxx.txt ，这一系列的提示词文件中。

![1750210529278](image/README/1750210529278.png)

推荐v0.85版提示词：

![1751893401750](image/README/1751893401750.png)

模型的选择，和温度的选择：

在主界面左上角设置：

![1754145609143](image/README/1754145609143.png)







7、点击“Markdown翻译（指定翻译成何种语言）”  按钮

![1750211053965](image/README/1750211053965.png)

聊天输入框的路径下一级子目录中的full.md，这种mineru的识别出来的md文件，会被批量翻译，

8、等一等就翻译完了

翻译完成的md文件会输出到对应的子目录中。

![1742017618813](image/README/1742017618813.png)

![1742017878111](image/README/1742017878111.png)

翻译完毕！

文件说明：

full.md  原始待翻译文件

full-中文翻译.md  兜底的翻译后的文件，带书名的md文件无法生成的会后备用。。

书名xxx.md  翻译后的文件。

9。优化翻译好的md文件（可选，可以不用）

H1，H2 感觉是不是有点太大，这个工具批量转换为H3。

拷贝文件到含有多个翻译完成的文件夹中的根目录中（与这些翻译好的文件夹平行，位于同一层），

![1742295545719](image/README/1742295545719.png)

双击运行，自动处理。

![1742229116216](image/README/1742229116216.png)

注意：使用qwq32b等带思考的模型翻译输出的思考的文字会自动去掉，只保留正式的翻译文本输出。

后续可以通过vscode 或者 obsidian 或者typora  或者pandoc进行输出成pdf文件。

#### markdown翻译其他配套工具

##### 翻译后的markdown优化.py

这个工具可以把当前目录翻译完成的各种markdown工作文件夹中的md文件进行优化。

目的：完善标签的显示，修复错误的标签层级（比如去掉翻译时候错误的给正文打的标题标签）

![1742904485532](image/README/1742904485532.png)

比如上图，就是大模型翻译中错误的标记为标题的正文，需要去掉标题标签，把这句话恢复为正文。

实践中发现，大多数大模型在翻译过程中都会有这种升级标题的操作，原因不明。。

![1742904245437](image/README/1742904245437.png)

处理完成后会像上图一样显示详细的统计信息。

这时候就是最终版的md文件了，可以愉快的直接阅读和导出pdf了。

![1742904343464](image/README/1742904343464.png)

##### mineru识别完毕的文件夹去掉后缀字符.py

![1742904799050](image/README/1742904799050.png)

mineru识别完毕的工作文件夹，会有很长的后缀，放在.pdf之前，文件名之后， 双击本程序可以自动去除当前文件夹（不包含子目录的文件名夹中的名字的后缀）

##### markdown翻译配套提示词.txt

我对翻译markdown文件的提示词的每一次更新，都会在这里进行对应记录，方便各位读者用户比较和使用。

##### 批量拆分pdf.py

双击可以将当前文件夹不包含子目录的pdf文件拆分成mieru客户端可以处理的大小。

属于预处理程序。

执行完毕后再选中拆分的文件拖动到mineru的工作框中进行ocr。

##### 批量导出md文件为pdf  (暂时还不完善，可以先不用他)

复制“markdown批量导出pdf.py”到目标目录中，

复制翻译完毕的需要导出pdf的完整的markdwon目录到这个目录，

![1743172562597](image/README/1743172562597.png)

双击，就可以自动批量导出markdown文件为pdf了。

![1743172640234](image/README/1743172640234.png)

### 2. arxriv批量论文翻译  （已经合并到本项目的学术gpt3.80中，可以直接调出插件来用了）

- 第一步：
- 输入框内输入要翻译的arxiv的id号，如2503.12345
- 多个id号用英文逗号隔开。
- ![1750211949659](image/README/1750211949659.png)

第二步：

右侧插件区域----打开下拉菜单-----选择：“arxiv论文精细翻译（输入arxivID）”----

![1750212053098](image/README/1750212053098.png)

第三步：

下方输入框输入专用的arxiv论文翻译的提示词

![1750212165102](image/README/1750212165102.png)

第四步：

点击：“arxiv论文精细翻译（输入arxivID）按钮 ”

自动批量逐个翻译刚才输入的arxivid们对应的论文。
