import re

class MathFormulaProtector:
    """
    公式保护与还原工具，支持常见的五种数学公式标签。
    按文本出现顺序编号，保证多类型混合时编号连续。
    """
    def __init__(self):
        # pattern, tag
        self.patterns = [
            (r'\\begin\{[a-zA-Z*]+\}[\s\S]*?\\end\{[a-zA-Z*]+\}', 'ENV'),
            (r'\$\$[\s\S]*?\$\$', 'BLOCK_DOLLAR'),
            (r'\\\[[\s\S]*?\\\]', 'BLOCK_BRACKET'),
            (r'(?<!\\)\$[^$]+?(?<!\\)\$(?!\\)', 'INLINE_DOLLAR'),
            (r'\\\([\s\S]*?\\\)', 'INLINE_PAREN'),
            (r'!\[.*?\]\(.*?\)', 'IMAGE'),  # 添加图片支持
        ]
        # 合成大pattern
        self.big_pattern = '|'.join(f'({p})' for p, _ in self.patterns)
        self.tag_list = [tag for _, tag in self.patterns]

    def protect(self, text):
        mapping = {}
        idx = 0
        def replacer(match):
            nonlocal idx
            # 找到命中的分组
            for i, tag in enumerate(self.tag_list):
                if match.group(i+1) is not None:
                    # 确保匹配的内容不为空
                    matched_text = match.group(0)
                    if matched_text and len(matched_text.strip()) > 0:
                        key = f"__FORMULA_{tag}_{idx}__"
                        mapping[key] = matched_text
                        idx += 1
                        return key
            return match.group(0)  # 不应到这里
        protected = re.sub(self.big_pattern, replacer, text, flags=re.DOTALL)
        return protected, mapping

    def restore(self, text, mapping):
        # 按key长度倒序替换，防止短key嵌套长key
        for key in sorted(mapping.keys(), key=lambda x: -len(x)):
            text = text.replace(key, mapping[key])
        return text
    
    def repair_slice_formulas(self, masked_slices, original_slices):
        """
        修复切片间的公式，只处理带掩码切片，原始切片保持原样
        
        Args:
            masked_slices: 带掩码的切片列表
            original_slices: 对应的参考原文切片列表
            
        Returns:
            tuple: (修复后的带掩码切片列表, 保持原样的原始切片列表, 修复记录)
        """
        # 实现增量处理，避免一次性加载所有切片
        # 只对带掩码切片进行处理，原始切片保持原样
        repair_records = []
        repaired_masked_slices = []
        # 原始切片保持原样
        repaired_original_slices = original_slices.copy()
        
        # 记录处理过程的详细信息
        process_details = []
        
        # 处理第一个切片（只处理带掩码切片）
        first_masked_slice_repaired, _, first_record = self._process_slice_head(
            masked_slices[0], original_slices[0])
        repaired_masked_slices.append(first_masked_slice_repaired)
        if first_record:
            repair_records.append(first_record)
        
        process_details.append({
            "slice_index": 0,
            "action": "process_head",
            "has_repair": first_record is not None
        })
        
        # 处理中间的切片（只处理带掩码切片）
        prev_trailing_masked_fragment = ""
        for i in range(1, len(masked_slices)):
            # 只合并前一个带掩码切片的尾部片段
            current_masked_slice = prev_trailing_masked_fragment + masked_slices[i]
            current_original_slice = original_slices[i]  # 原始切片保持原样，不合并片段
            
            # 处理带掩码切片头部
            current_masked_slice_repaired, _, head_record = self._process_slice_head(
                current_masked_slice, current_original_slice)
            if head_record:
                repair_records.append(head_record)
            
            # 处理带掩码切片尾部，提取未闭合的公式片段
            final_masked_slice, _, trailing_masked_fragment, _, tail_record = self._process_slice_tail(
                current_masked_slice_repaired, current_original_slice)
            if tail_record:
                repair_records.append(tail_record)
            
            repaired_masked_slices.append(final_masked_slice)
            # 原始切片保持原样，不进行尾部处理
            repaired_original_slices[i] = current_original_slice
            prev_trailing_masked_fragment = trailing_masked_fragment
            
            # 记录处理详情
            process_details.append({
                "slice_index": i,
                "action": "process_head_and_tail",
                "has_head_repair": head_record is not None,
                "has_tail_repair": tail_record is not None,
                "has_trailing_fragment": len(trailing_masked_fragment) > 0
            })
            
        # 处理最后一个切片的尾部片段
        if prev_trailing_masked_fragment:
            last_record = {
                "type": "trailing_fragment",
                "masked_content": prev_trailing_masked_fragment,
                "action": "removed"
            }
            repair_records.append(last_record)
            
            process_details.append({
                "slice_index": "last",
                "action": "process_trailing_fragment",
                "fragment_content": prev_trailing_masked_fragment
            })
        
        # 将处理详情添加到修复记录中
        process_record = {
            "type": "process_details",
            "action": "process_summary",
            "details": process_details
        }
        repair_records.append(process_record)
            
        return repaired_masked_slices, repaired_original_slices, repair_records
        
    def _process_slice_head(self, masked_slice_text, original_slice_text):
        """
        处理切片头部的公式
        
        Args:
            masked_slice_text: 带掩码的切片文本
            original_slice_text: 对应的参考原文切片文本
            
        Returns:
            tuple: (处理后的带掩码切片文本, 原始切片文本, 修复记录)
        """
        # 第一步：识别闭合公式并转为掩码（使用FIX_前缀）
        repaired_masked_text, mapping = self.identify_closed_formula(masked_slice_text)
        
        # 第二步：主动闭合潜在公式
        # （在当前上下文中暂不实现）
        
        # 第三步：删除不能闭合的孤立公式符号
        final_masked_text = self.remove_isolated_formulas(repaired_masked_text)
        
        # 生成修复记录
        record = None
        if masked_slice_text != final_masked_text:
            record = {
                "type": "head_processing",
                "original_masked": masked_slice_text[:100] + "..." if len(masked_slice_text) > 100 else masked_slice_text,
                "repaired_masked": final_masked_text[:100] + "..." if len(final_masked_text) > 100 else final_masked_text,
                "original_text": original_slice_text[:100] + "..." if len(original_slice_text) > 100 else original_slice_text,
                "action": "formula_repair"
            }
            
        return final_masked_text, original_slice_text, record
        
    def _process_slice_tail(self, masked_slice_text, original_slice_text):
        """
        处理切片尾部，提取未闭合的公式片段
        
        Args:
            masked_slice_text: 带掩码的切片文本
            original_slice_text: 对应的参考原文切片文本
            
        Returns:
            tuple: (处理后的带掩码切片文本, 原始切片文本, 
                   提取的带掩码尾部片段, 空字符串, 修复记录)
        """
        # 查找最后一个掩码的位置
        last_mask_pos = masked_slice_text.rfind("__FORMULA_")
        if last_mask_pos == -1:
            last_mask_pos = 0
            
        # 从最后一个掩码之后查找未闭合的公式
        tail_masked_text = masked_slice_text[last_mask_pos:]
        tail_original_text = original_slice_text[last_mask_pos:] if last_mask_pos < len(original_slice_text) else ""
        
        # 简化的实现：查找是否有未闭合的$$符号
        dollar_pos = tail_masked_text.rfind("$$")
        if dollar_pos != -1 and dollar_pos == tail_masked_text.rfind("$$", 0, dollar_pos):
            # 存在未闭合的$$符号
            masked_fragment = tail_masked_text[dollar_pos:]
            original_fragment = tail_original_text[dollar_pos:] if dollar_pos < len(tail_original_text) else ""
            
            processed_masked_text = masked_slice_text[:last_mask_pos + len(masked_slice_text[last_mask_pos:]) - len(masked_fragment)]
            # 原始切片保持原样，不进行尾部处理
            processed_original_text = original_slice_text
            
            record = {
                "type": "tail_processing",
                "masked_content": masked_fragment,
                "action": "extracted"
            }
            
            return processed_masked_text, processed_original_text, masked_fragment, "", record
            
        return masked_slice_text, original_slice_text, "", "", None
        
    def identify_closed_formula(self, text):
        """
        识别文本开头的闭合公式并生成FIX掩码
        
        Args:
            text: 待处理文本
            
        Returns:
            tuple: (处理后的文本, 公式映射)
        """
        # 查找第一个掩码的位置
        first_mask_pos = text.find("__FORMULA_")
        if first_mask_pos == -1:
            first_mask_pos = len(text)
            
        # 获取掩码前的文本
        prefix_text = text[:first_mask_pos]
        
        # 在前缀文本中查找闭合公式并生成FIX掩码
        mapping = {}
        idx = 0
        
        def fix_replacer(match):
            nonlocal idx
            # 找到命中的分组
            for i, (pattern, tag) in enumerate(self.patterns):
                if match.group(i+1) is not None:
                    # 确保匹配的内容不为空
                    matched_text = match.group(0)
                    if matched_text and len(matched_text.strip()) > 0:
                        key = f"__FORMULA_FIX_{tag}_{idx}__"
                        mapping[key] = matched_text
                        idx += 1
                        return key
            return match.group(0)
            
        # 只处理前缀文本中的公式
        protected_prefix = re.sub(self.big_pattern, fix_replacer, prefix_text, flags=re.DOTALL)
        
        # 重新组合文本
        protected = protected_prefix + text[first_mask_pos:]
        return protected, mapping
        
    def remove_isolated_formulas(self, text):
        """
        删除孤立的公式符号
        
        Args:
            text: 待处理文本
            
        Returns:
            处理后的文本
        """
        # 删除孤立的$$符号（每行只有$$符号的情况）
        lines = text.split('\n')
        processed_lines = []
        for line in lines:
            # 如果一行只有$$符号，则删除该行
            if line.strip() == "$$":
                continue
            processed_lines.append(line)
        return '\n'.join(processed_lines)
        
    def _sync_original_slice(self, original_text, original_masked_text, repaired_masked_text):
        """
        同步处理参考原文切片，使其与带掩码切片的修复结果保持一致
        
        Args:
            original_text: 原始参考原文切片
            original_masked_text: 原始带掩码切片
            repaired_masked_text: 修复后的带掩码切片
            
        Returns:
            处理后的参考原文切片
        """
        # 根据新需求，原始切片保持原样不处理
        return original_text
