# 此Dockerfile适用于“无本地模型”的环境构建，如果需要使用chatglm等本地模型，请参考 docs/Dockerfile+ChatGLM
# 如何构建: 先修改 `config.py`， 然后 docker build -t gpt-academic-nolocal -f docs/Dockerfile+NoLocal .
# 如何运行: docker run --rm -it --net=host gpt-academic-nolocal
FROM python:3.11

# 指定路径
WORKDIR /gpt

# 装载项目文件
COPY . .

# 安装依赖
RUN pip3 install -r requirements.txt

# 安装语音插件的额外依赖
RUN pip3 install aliyun-python-sdk-core==2.13.3 pyOpenSSL webrtcvad scipy git+https://github.com/aliyun/alibabacloud-nls-python-sdk.git

# edge-tts需要的依赖
RUN apt update && apt install ffmpeg -y

# 可选步骤，用于预热模块
RUN python3  -c 'from check_proxy import warm_up_modules; warm_up_modules()'

# 启动
CMD ["python3", "-u", "main.py"]
