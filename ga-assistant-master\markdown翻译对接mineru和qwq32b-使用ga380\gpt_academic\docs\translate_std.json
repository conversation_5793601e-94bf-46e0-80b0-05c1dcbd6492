{"解析JupyterNotebook": "ParsingJupyterNotebook", "Latex翻译中文并重新编译PDF": "TranslateChineseToEnglishInLatexAndRecompilePDF", "联网的ChatGPT_bing版": "OnlineChatGPT_BingEdition", "理解PDF文档内容标准文件输入": "UnderstandPdfDocumentContentStandardFileInput", "Latex英文纠错加PDF对比": "CorrectEnglishInLatexWithPDFComparison", "下载arxiv论文并翻译摘要": "DownloadArxivPaperAndTranslateAbstract", "Markdown翻译指定语言": "TranslateMarkdownToSpecifiedLanguage", "PDF_Translate": "BatchTranslatePDFDocuments_MultiThreaded", "下载arxiv论文翻译摘要": "DownloadArxivPaperTranslateAbstract", "解析一个Python项目": "ParsePythonProject", "解析一个Golang项目": "ParseGolangProject", "代码重写为全英文_多线程": "RewriteCodeToEnglish_MultiThreaded", "解析一个CSharp项目": "ParsingCSharpProject", "删除所有本地对话历史记录": "DeleteAllLocalConversationHistoryRecords", "Markdown_Translate": "BatchTranslateMarkdown", "连接bing搜索回答问题": "ConnectBingSearchAnswerQuestion", "Langchain知识库": "LangchainKnowledgeBase", "Latex_Function": "OutputPDFFromLatex", "把字符太少的块清除为回车": "ClearBlocksWithTooFewCharactersToNewline", "Latex精细分解与转化": "DecomposeAndConvertLatex", "解析一个C项目的头文件": "ParseCProjectHeaderFiles", "Markdown英译中": "TranslateMarkdownFromEnglishToChinese", "Markdown中译英": "MarkdownChineseToEnglish", "数学动画生成manim": "MathematicalAnimationGenerationManim", "chatglm微调工具": "ChatGLMFineTuningTool", "解析一个Rust项目": "ParseRustProject", "解析一个Java项目": "ParseJavaProject", "联网的ChatGPT": "ChatGPTConnectedToNetwork", "解析任意code项目": "ParseAnyCodeProject", "合并小写开头的段落块": "MergeLowercaseStartingParagraphBlocks", "Latex英文润色": "EnglishProofreadingForLatex", "Latex全文润色": "FullTextProofreadingForLatex", "询问多个大语言模型": "InquiryMultipleLargeLanguageModels", "解析一个Lua项目": "ParsingLuaProject", "解析ipynb文件": "ParsingIpynbFiles", "批量总结PDF文档": "BatchSummarizePDFDocuments", "批量翻译PDF文档": "BatchTranslatePDFDocuments", "理解PDF文档内容": "UnderstandPdfDocumentContent", "Latex中文润色": "LatexChineseProofreading", "Latex英文纠错": "LatexEnglishCorrection", "Latex全文翻译": "LatexFullTextTranslation", "同时问询_指定模型": "InquireSimultaneously_SpecifiedModel", "批量生成函数注释": "BatchGenerateFunctionComments", "解析一个前端项目": "ParseFrontendProject", "高阶功能模板函数": "HighOrderFunctionTemplateFunctions", "高级功能函数模板": "AdvancedFunctionTemplate", "总结word文档": "SummarizingWordDocuments", "载入Conversation_To_File": "LoadConversationHistoryArchive", "Latex中译英": "LatexChineseToEnglish", "Latex英译中": "LatexEnglishToChinese", "连接网络回答问题": "ConnectToNetworkToAnswerQuestions", "交互功能模板函数": "InteractiveFunctionTemplateFunction", "交互功能函数模板": "InteractiveFunctionFunctionTemplate", "sprint亮靛": "SprintIndigo", "print亮黄": "PrintBrightYellow", "print亮绿": "PrintBrightGreen", "print亮红": "PrintBrightRed", "解析项目源代码": "ParseProjectSourceCode", "解析一个C项目": "ParseCProject", "全项目切换英文": "SwitchToEnglishForTheWholeProject", "谷歌检索小助手": "GoogleSearchAssistant", "读取知识库作答": "ReadKnowledgeArchiveAnswerQuestions", "print亮蓝": "PrintBrightBlue", "微调数据集生成": "FineTuneDatasetGeneration", "清理多余的空行": "CleanUpExcessBlankLines", "编译Latex": "CompileLatex", "解析Paper": "ParsePaper", "ipynb解释": "IpynbExplanation", "读文章写摘要": "ReadArticleWriteSummary", "生成函数注释": "GenerateFunctionComments", "解析项目本身": "ParseProjectItself", "Conversation_To_File": "ConversationHistoryArchive", "专业词汇声明": "ProfessionalTerminologyDeclaration", "解析docx": "ParseDocx", "解析源代码新": "ParsingSourceCodeNew", "总结音视频": "SummaryAudioVideo", "知识库问答": "UpdateKnowledgeArchive", "多文件润色": "ProofreadMultipleFiles", "多文件翻译": "TranslateMultipleFiles", "解析PDF": "ParsePDF", "同时问询": "SimultaneousInquiry", "图片生成": "ImageGeneration", "动画生成": "AnimationGeneration", "语音助手": "VoiceAssistant", "启动微调": "StartFineTuning", "清除缓存": "ClearCache", "辅助功能": "Accessibility", "虚空终端": "VoidTerminal", "解析PDF_基于GROBID": "ParsePDF_BasedOnGROBID", "虚空终端主路由": "VoidTerminalMainRoute", "批量翻译PDF文档_NOUGAT": "BatchTranslatePDFDocuments_NOUGAT", "解析PDF_基于NOUGAT": "ParsePDF_NOUGAT", "解析一个Matlab项目": "AnalyzeAMatlabProject", "函数动态生成": "DynamicFunctionGeneration", "多智能体终端": "MultiAgentTerminal", "多智能体": "MultiAgent", "图片生成_DALLE2": "ImageGeneration_DALLE2", "图片生成_DALLE3": "ImageGeneration_DALLE3", "图片修改_DALLE2": "ImageModification_DALLE2", "生成多种Mermaid图表": "GenerateMultipleMermaidCharts", "知识库文件注入": "InjectKnowledgeBaseFiles", "PDF翻译中文并重新编译PDF": "TranslatePDFToChineseAndRecompilePDF", "随机小游戏": "RandomMiniGame", "互动小游戏": "InteractiveMiniGame", "解析历史输入": "ParseHistoricalInput", "高阶功能模板函数示意图": "HighOrderFunctionTemplateDiagram"}