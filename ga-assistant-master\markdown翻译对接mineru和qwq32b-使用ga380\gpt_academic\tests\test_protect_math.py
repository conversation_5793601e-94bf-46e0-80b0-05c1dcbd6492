import unittest
import re
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from crazy_functions.protect_math import MathFormulaProtector

class TestMathFormulaProtector(unittest.TestCase):
    def setUp(self):
        self.protector = MathFormulaProtector()
    
    def test_basic_block_dollar_formulas(self):
        """测试基本的$$...$$块级公式保护"""
        text = "This is a test with $$E = mc^2$$ formula in the middle."
        protected, mapping = self.protector.protect(text)
        
        # 检查是否正确替换了公式
        self.assertIn("FORMULA_BLOCK_DOLLAR", protected)
        self.assertEqual(len(mapping), 1)
        
        # 检查是否能正确恢复
        restored = self.protector.restore(protected, mapping)
        self.assertEqual(restored, text)
    
    def test_multiple_block_dollar_formulas(self):
        """测试多个$$...$$块级公式保护"""
        text = """Here we have multiple formulas:
        $$a^2 + b^2 = c^2$$ and also $$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$
        And another one: $$\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}$$"""
        
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 3)
    
    def test_inline_dollar_formulas(self):
        """测试行内$...$公式保护"""
        text = "The famous equation $E = mc^2$ is well known. Another formula $a^2 + b^2 = c^2$ is also popular."
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 2)
    
    def test_inline_paren_formulas(self):
        """测试行内\\(...\\)公式保护"""
        text = "Using parentheses: \\(E = mc^2\\) and \\(\\sum_{i=1}^n i = \\frac{n(n+1)}{2}\\)"
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 2)
    
    def test_block_bracket_formulas(self):
        """测试块级\\[...\\]公式保护"""
        text = """A block formula:
        \\[\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}\\]
        And another one:
        \\[\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}\\]"""
        
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 2)
    
    def test_latex_environment_formulas(self):
        """测试LaTeX环境公式保护"""
        text = """An align environment:
        \\begin{align}
        x &= y + z \\\\
        y &= a + b
        \\end{align}
        And an equation environment:
        \\begin{equation}
        E = mc^2
        \\end{equation}"""
        
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 2)
    
    def test_complex_mixed_formulas(self):
        """测试复杂的混合公式形式"""
        text = """In mathematics, the Cauchy-Schwarz inequality states that for all vectors $u$ and $v$ in an inner product space,
        $$|\\langle u, v \\rangle|^2 \\leq \\langle u, u \\rangle \\cdot \\langle v, v \\rangle$$
        This can also be written in Leibniz notation as:
        \\[
        \\left(\\int_{-\\infty}^{\\infty} f(x)g(x)\\,dx\\right)^2 \\leq \\int_{-\\infty}^{\\infty} f(x)^2\\,dx \\cdot \\int_{-\\infty}^{\\infty} g(x)^2\\,dx
        \\]
        The proof involves considering the quadratic function:
        \\begin{equation}
        q(\\lambda) = \\langle u - \\lambda v, u - \\lambda v \\rangle = \\|u\\|^2 - 2\\lambda\\langle u, v \\rangle + \\lambda^2\\|v\\|^2
        \\end{equation}
        Since $q(\\lambda) \\geq 0$ for all real $\\lambda$, the discriminant must be non-positive:
        \\(\\langle u, v \\rangle^2 - \\|u\\|^2\\|v\\|^2 \\leq 0\\)"""
        
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        # 打印调试信息
        print("\n=== test_complex_mixed_formulas 调试信息 ===")
        print(f"找到的公式数量: {len(mapping)}")
        print("所有匹配的公式:")
        for i, (key, value) in enumerate(mapping.items()):
            print(f"  {i+1}. {key}: {value}")
        print("========================================\n")
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 5)
    
    def test_large_text_performance(self):
        """测试大型文本的性能和正确性"""
        # 创建一个包含2000多个英文单词的大型文本
        large_text_base = """
        In mathematics, the concept of limit is fundamental to calculus and analysis. The limit of a function, 
        as its input approaches some value, describes the behavior of the function near that point. Limits are 
        essential for defining continuity, derivatives, and integrals. The formal definition of a limit was 
        developed by Augustin-Louis Cauchy and Karl Weierstrass in the 19th century. This rigorous approach 
        replaced earlier intuitive notions of limits. A function f(x) approaches the limit L as x approaches a, 
        if for every positive number epsilon, there exists a positive number delta such that whenever the 
        distance between x and a is less than delta, the distance between f(x) and L is less than epsilon. 
        This is expressed mathematically as $$\\lim_{x \\to a} f(x) = L$$. The epsilon-delta definition 
        provides a precise way to handle the concept of approaching a value. Limits can be evaluated using 
        various techniques including direct substitution, factoring, rationalization, and L'Hôpital's rule. 
        One-sided limits are important when a function behaves differently from the left and right sides 
        of a point. Infinite limits describe situations where a function grows without bound. Limits at 
        infinity examine the behavior of functions as the input becomes arbitrarily large. The squeeze 
        theorem is a powerful tool for evaluating limits of functions that are difficult to compute directly. 
        Sequences also have limits, which describe their long-term behavior. A sequence converges if it 
        approaches a specific value, and diverges otherwise. Series are defined as the sum of the terms 
        of an infinite sequence. The convergence of series is determined by various tests including the 
        ratio test, root test, and comparison test. Power series represent functions as infinite polynomials. 
        Taylor series provide polynomial approximations to functions near a point. Fourier series represent 
        periodic functions as sums of sine and cosine functions. The study of limits extends to functions 
        of several variables, where limits can depend on the path of approach. Complex analysis deals with 
        limits of functions of complex variables. Measure theory provides a rigorous foundation for 
        integration and probability theory. Topological spaces generalize the concept of limits beyond 
        metric spaces. Nets and filters are used to define limits in general topological spaces. The 
        concept of limit has applications in physics, engineering, economics, and computer science. In 
        numerical analysis, limits are used to analyze the convergence of algorithms. In probability 
        theory, limits are used to define concepts such as convergence in probability and almost sure 
        convergence. The law of large numbers and the central limit theorem are fundamental results 
        involving limits. In dynamical systems, limits describe the long-term behavior of systems. 
        Attractors are sets of states toward which a system evolves over time. The concept of limit 
        also appears in category theory, where limits and colimits generalize the notions from set 
        theory and topology. These abstract concepts have concrete applications in algebra, geometry, 
        and logic. The historical development of the concept of limit reflects the evolution of 
        mathematical rigor. Early mathematicians like Newton and Leibniz used intuitive notions of 
        limits in their development of calculus. Later mathematicians such as Cauchy, Weierstrass, 
        and Dedekind provided rigorous foundations. The modern definition of limit is the result of 
        centuries of mathematical development and refinement. Understanding limits is crucial for 
        advanced mathematical study and research. The applications of limits continue to expand 
        into new areas of science and technology. In machine learning, limits are used in the 
        analysis of optimization algorithms and neural network convergence. In quantum mechanics, 
        limits describe the transition from quantum to classical behavior. In relativity theory, 
        limits describe the behavior of objects approaching the speed of light. The concept of 
        limit remains central to mathematical thinking and problem solving. Mastery of limits 
        provides a foundation for understanding more advanced mathematical concepts. The rigorous 
        treatment of limits develops logical reasoning and proof-writing skills. These skills 
        are valuable in many areas beyond pure mathematics. The epsilon-delta definition, while 
        challenging, provides a template for precise mathematical reasoning. Students who master 
        this definition develop a deeper appreciation for mathematical rigor. The applications 
        of limits in science and engineering demonstrate the power of abstract mathematical 
        concepts to solve real-world problems. Modern computational tools allow for numerical 
        exploration of limits and visualization of their properties. These tools complement 
        theoretical understanding and provide intuition for abstract concepts. The study of 
        limits continues to evolve with new applications and theoretical developments. Research 
        in analysis, topology, and related fields continues to refine our understanding of 
        limits and their applications. The interplay between discrete and continuous mathematics 
        involves limits in fundamental ways. The digital approximation of continuous phenomena 
        relies on limit processes. Computer graphics use limits to render smooth curves and 
        surfaces. Signal processing uses limits in the analysis of continuous signals. The 
        Fourier transform, which decomposes signals into frequency components, involves limits. 
        Wavelets provide alternative representations that also involve limit processes. In 
        statistics, limits are used in the derivation of asymptotic properties of estimators. 
        The maximum likelihood estimator, under regularity conditions, converges to the true 
        parameter value as the sample size increases. This convergence is described using 
        limits. Bootstrap methods use resampling techniques that involve limit theorems. The 
        delta method uses Taylor expansions and limits to approximate the distribution of 
        functions of estimators. These statistical applications demonstrate the practical 
        importance of limit theory. In economics, limits describe equilibrium conditions and 
        optimization problems. The concept of marginal analysis involves limits. Consumer 
        optimization problems use limits to define marginal utility. Producer optimization 
        problems use limits to define marginal cost and marginal revenue. Game theory uses 
        limits to define equilibrium concepts. The Nash equilibrium involves limits in its 
        definition and computation. Evolutionary game theory uses limits to describe the 
        long-term behavior of populations. In computer science, limits describe the 
        asymptotic behavior of algorithms. The Big O notation, which describes algorithmic 
        complexity, is defined using limits. The analysis of recursive algorithms often 
        involves solving recurrence relations using generating functions and limits. The 
        Master theorem for divide-and-conquer algorithms involves limits in its derivation. 
        In numerical analysis, limits describe the convergence of iterative methods. The 
        Newton-Raphson method for finding roots involves limits. The convergence of 
        numerical integration methods is described using limits. The stability of 
        numerical methods is analyzed using limits. These applications show that limits 
        are essential tools in computational mathematics. In physics, limits describe 
        idealized situations and approximations. The concept of instantaneous velocity 
        is defined using limits. The definition of acceleration involves limits. The 
        concept of work in physics involves limits in its definition as the integral 
        of force over distance. The definition of electric field involves limits as 
        the test charge approaches zero. The concept of temperature involves limits 
        in statistical mechanics. The partition function, which describes the 
        statistical properties of a system, involves limits. The thermodynamic limit 
        describes the behavior of systems as the number of particles approaches 
        infinity. These physical applications demonstrate the fundamental role of 
        limits in describing natural phenomena. In engineering, limits are used in 
        the analysis and design of systems. Control theory uses limits to describe 
        system stability. The concept of steady state involves limits. The frequency 
        response of systems involves limits. The Laplace transform, which is used 
        to solve differential equations, involves limits. The Fourier transform 
        also involves limits. These transforms are essential tools in engineering 
        analysis and design. In signal processing, limits describe the behavior 
        of filters and systems. The concept of bandwidth involves limits. The 
        sampling theorem, which describes how to convert continuous signals to 
        discrete sequences, involves limits. The reconstruction of continuous 
        signals from samples involves limits. These concepts are fundamental to 
        digital signal processing. In communications engineering, limits describe 
        the capacity of communication channels. The Shannon capacity formula 
        involves limits. The analysis of error-correcting codes involves limits. 
        The performance of communication systems in the presence of noise is 
        analyzed using limits. These applications show the importance of limits 
        in modern communication systems. The mathematical theory of limits 
        continues to develop with new applications and insights. Research in 
        functional analysis extends limit concepts to infinite-dimensional spaces. 
        The study of Banach spaces and Hilbert spaces involves limits in 
        fundamental ways. Operator theory, which studies linear transformations 
        on function spaces, uses limits extensively. The spectral theory of 
        operators involves limits. These abstract concepts have concrete 
        applications in quantum mechanics and other areas of physics. The 
        development of measure theory provided a rigorous foundation for 
        integration and probability theory. The Lebesgue integral, which 
        generalizes the Riemann integral, is defined using limits. The 
        convergence theorems for the Lebesgue integral involve limits. 
        Probability theory uses measure theory and limits to define 
        concepts such as expectation and variance. The law of large 
        numbers and the central limit theorem are fundamental results 
        involving limits. These results provide the theoretical 
        foundation for statistical inference. The applications of 
        limits in probability and statistics are essential for data 
        analysis and scientific inference. In differential equations, 
        limits are used to define derivatives and solutions. The 
        concept of a derivative involves a limit. The definition of 
        continuity involves limits. The solution of differential 
        equations often involves limits in series solutions. The 
        method of characteristics for partial differential equations 
        involves limits. The finite element method for solving partial 
        differential equations involves limits as the mesh size 
        approaches zero. These numerical methods are essential tools 
        in engineering and scientific computing. The applications of 
        limits continue to expand into new areas of research. In 
        machine learning, limits are used in the analysis of 
        optimization algorithms. The convergence of gradient descent 
        methods involves limits. The analysis of neural network 
        training involves limits. The universal approximation 
        theorems for neural networks involve limits. These results 
        provide theoretical foundations for machine learning methods. 
        In data science, limits are used in the analysis of large 
        datasets. The law of large numbers justifies the use of 
        sample averages to estimate population parameters. The 
        central limit theorem justifies the use of normal 
        approximations for statistical inference. These results 
        are fundamental to data analysis. The bootstrap method, 
        which is used to estimate the variability of statistical 
        estimates, involves limits. The asymptotic theory of 
        statistical inference involves limits. These concepts 
        are essential for modern data science. The mathematical 
        theory of limits provides a unifying framework for these 
        diverse applications. The rigorous treatment of limits 
        develops logical reasoning skills that are valuable in 
        many areas beyond mathematics. The applications of limits 
        demonstrate the power of abstract mathematical concepts to 
        solve real-world problems. The continued development of 
        limit theory provides new tools for scientific and 
        engineering applications. The interplay between theory 
        and application drives the continued evolution of 
        mathematical knowledge. The study of limits remains a 
        central topic in mathematical education and research.
        """
        
        # 添加各种数学公式到大型文本中
        large_text_with_formulas = large_text_base + """
        Some mathematical expressions to test:
        $$\\int_{0}^{\\infty} \\frac{\\sin x}{x} dx = \\frac{\\pi}{2}$$
        This is an important integral in analysis. Another formula is $e^{i\\pi} + 1 = 0$,
        which is Euler's identity. We also have \\(\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}\\)
        which is the Basel problem. A more complex expression is:
        \\[
        \\zeta(s) = \\sum_{n=1}^{\\infty} \\frac{1}{n^s} = \\prod_{p \\text{ prime}} \\frac{1}{1-p^{-s}}
        \\]
        This is the Riemann zeta function and its Euler product formula. The Gaussian integral is:
        $$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$
        In an align environment:
        \\begin{align}
        \\nabla \\cdot \\mathbf{E} &= \\frac{\\rho}{\\varepsilon_0} \\\\
        \\nabla \\cdot \\mathbf{B} &= 0 \\\\
        \\nabla \\times \\mathbf{E} &= -\\frac{\\partial \\mathbf{B}}{\\partial t} \\\\
        \\nabla \\times \\mathbf{B} &= \\mu_0\\mathbf{J} + \\mu_0\\varepsilon_0\\frac{\\partial \\mathbf{E}}{\\partial t}
        \\end{align}
        These are Maxwell's equations in differential form. And finally, Schrödinger's equation:
        \\begin{equation}
        i\\hbar\\frac{\\partial}{\\partial t}\\Psi(\\mathbf{r},t) = \\hat{H}\\Psi(\\mathbf{r},t)
        \\end{equation}
        """
        
        protected, mapping = self.protector.protect(large_text_with_formulas)
        restored = self.protector.restore(protected, mapping)
        
        # 验证恢复的文本与原始文本相同
        self.assertEqual(restored, large_text_with_formulas)
        
        # 打印调试信息
        print("\n=== test_large_text_performance 调试信息 ===")
        print(f"找到的公式数量: {len(mapping)}")
        print("所有匹配的公式:")
        for i, (key, value) in enumerate(mapping.items()):
            print(f"  {i+1}. {key}: {value[:50]}{'...' if len(value) > 50 else ''}")
        print("========================================\n")
        
        # 验证找到了所有预期的公式（应该有7个）
        self.assertEqual(len(mapping), 7)
        
        # 验证公式类型
        tag_counts = {}
        for key in mapping.keys():
            tag = key.split('_')[2]  # 获取标签类型
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # 应该有: 3个BLOCK_DOLLAR, 1个INLINE_DOLLAR, 1个INLINE_PAREN, 1个BLOCK_BRACKET, 2个ENV
        self.assertEqual(tag_counts['BLOCK_DOLLAR'], 3)
        self.assertEqual(tag_counts['INLINE_DOLLAR'], 1)
        self.assertEqual(tag_counts['INLINE_PAREN'], 1)
        self.assertEqual(tag_counts['BLOCK_BRACKET'], 1)
        self.assertEqual(tag_counts['ENV'], 2)
    
    def test_nested_formulas(self):
        """测试嵌套公式的情况"""
        text = "A complex example with nested formulas: $$\\text{The solution is } x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a} \\text{ where } a=1, b=2, c=\\sin(\\theta)$$"
        protected, mapping = self.protector.protect(text)
        restored = self.protector.restore(protected, mapping)
        
        self.assertEqual(restored, text)
        self.assertEqual(len(mapping), 1)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空文本
        empty_text = ""
        protected, mapping = self.protector.protect(empty_text)
        restored = self.protector.restore(protected, mapping)
        self.assertEqual(restored, empty_text)
        
        # 没有公式
        no_formula_text = "This is a text without any mathematical formulas."
        protected, mapping = self.protector.protect(no_formula_text)
        restored = self.protector.restore(protected, mapping)
        self.assertEqual(restored, no_formula_text)
        self.assertEqual(len(mapping), 0)
        
        # 只有公式
        only_formula_text = "$$E = mc^2$$"
        protected, mapping = self.protector.protect(only_formula_text)
        restored = self.protector.restore(protected, mapping)
        self.assertEqual(restored, only_formula_text)
        self.assertEqual(len(mapping), 1)

def visualize_protection_results(original_text, protected_text, mapping):
    """可视化保护结果"""
    print("\n" + "="*80)
    print("FORMULA PROTECTION VISUALIZATION REPORT")
    print("="*80)
    
    print(f"\n1. ORIGINAL TEXT LENGTH: {len(original_text)} characters")
    print(f"2. PROTECTED TEXT LENGTH: {len(protected_text)} characters")
    print(f"3. TOTAL FORMULAS FOUND: {len(mapping)}")
    
    # 统计公式类型
    tag_counts = {}
    for key in mapping.keys():
        tag = key.split('_')[2]  # 获取标签类型
        tag_counts[tag] = tag_counts.get(tag, 0) + 1
    
    print("\n4. FORMULA TYPE DISTRIBUTION:")
    for tag, count in tag_counts.items():
        print(f"   - {tag}: {count}")
    
    # 显示前几个映射示例
    print("\n5. PROTECTION MAPPING EXAMPLES:")
    for i, (key, value) in enumerate(list(mapping.items())[:5]):
        print(f"   Example {i+1}:")
        print(f"     Mask: {key}")
        print(f"     Original: {value[:50]}{'...' if len(value) > 50 else ''}")
    
    # 计算保护效果
    total_formula_chars = sum(len(formula) for formula in mapping.values())
    print(f"\n6. PROTECTION EFFECTIVENESS:")
    print(f"   - Total formula characters protected: {total_formula_chars}")
    print(f"   - Mask characters used: {sum(len(key) for key in mapping.keys())}")
    print(f"   - Compression ratio: {total_formula_chars / sum(len(key) for key in mapping.keys()):.2f}" if mapping else "N/A")
    
    # 彩色可视化（使用ANSI颜色代码）
    print("\n7. COLOR-CODED VISUALIZATION:")
    print("   \033[92mGREEN\033[0m = Protected formulas")
    print("   \033[94mBLUE\033[0m  = Regular text")
    
    # 创建彩色可视化示例（仅显示前500个字符）
    sample_text = protected_text[:500]
    colored_sample = sample_text
    for key in sorted(mapping.keys(), key=lambda x: -len(x)):  # 按长度降序替换
        colored_sample = colored_sample.replace(key, f"\033[92m{key}\033[0m")
    
    print(f"\n8. SAMPLE PROTECTED TEXT (first 500 chars):")
    print(colored_sample)
    print("\n" + "="*80)

def run_comprehensive_test():
    """运行综合测试并提供可视化报告"""
    protector = MathFormulaProtector()
    
    # 创建一个大型测试用例
    comprehensive_test_text = """
    In vector calculus, the mathematical framework for describing physical fields, we encounter numerous 
    sophisticated concepts. The gradient operator, denoted by $\\nabla f$, represents the direction and 
    rate of fastest increase of a scalar field. For a vector field $\\mathbf{F}$, the divergence $\\nabla \\cdot \\mathbf{F}$ 
    measures the magnitude of a source or sink at a given point. The curl operator $\\nabla \\times \\mathbf{F}$ 
    describes the rotation of the field. These operators satisfy fundamental identities such as:
    $$\\nabla \\times (\\nabla f) = \\mathbf{0}$$
    This identity states that the curl of a gradient is always zero. Another important identity is:
    $$\\nabla \\cdot (\\nabla \\times \\mathbf{F}) = 0$$
    which means the divergence of a curl is always zero. The Laplacian operator is defined as:
    \\[
    \\nabla^2 f = \\nabla \\cdot \\nabla f = \\frac{\\partial^2 f}{\\partial x^2} + \\frac{\\partial^2 f}{\\partial y^2} + \\frac{\\partial^2 f}{\\partial z^2}
    \\]
    In integral form, the fundamental theorems of vector calculus relate volume, surface, and line integrals.
    The divergence theorem states:
    \\begin{equation}
    \\iiint_V (\\nabla \\cdot \\mathbf{F}) \\, dV = \\iint_S (\\mathbf{F} \\cdot \\mathbf{n}) \\, dS
    \\end{equation}
    Stokes' theorem relates surface and line integrals:
    \\begin{align}
    \\iint_S (\\nabla \\times \\mathbf{F}) \\cdot d\\mathbf{S} &= \\oint_C \\mathbf{F} \\cdot d\\mathbf{r} \\\\
    \\text{where } C &= \\text{boundary of surface } S
    \\end{align}
    Complex analysis extends these concepts to functions of complex variables. The Cauchy-Riemann equations:
    \\(
    \\frac{\\partial u}{\\partial x} = \\frac{\\partial v}{\\partial y} \\text{ and } \\frac{\\partial u}{\\partial y} = -\\frac{\\partial v}{\\partial x}
    \\)
    determine when a complex function $f(z) = u(x,y) + iv(x,y)$ is analytic. The Cauchy integral formula:
    $$f(a) = \\frac{1}{2\\pi i} \\oint_\\gamma \\frac{f(z)}{z-a} dz$$
    provides a powerful tool for evaluating complex integrals. The residue theorem generalizes this:
    \\[
    \\oint_\\gamma f(z) dz = 2\\pi i \\sum_{k=1}^n \\operatorname{Res}(f, a_k)
    \\]
    Fourier analysis decomposes functions into sinusoidal components. The Fourier transform:
    $$\\hat{f}(\\xi) = \\int_{-\\infty}^{\\infty} f(x) e^{-2\\pi i x \\xi} dx$$
    has numerous applications in signal processing and differential equations. The inverse transform is:
    \\(f(x) = \\int_{-\\infty}^{\\infty} \\hat{f}(\\xi) e^{2\\pi i x \\xi} d\\xi\\)
    Partial differential equations describe many physical phenomena. The heat equation:
    \\begin{equation}
    \\frac{\\partial u}{\\partial t} = \\alpha \\nabla^2 u
    \\end{equation}
    models heat diffusion. The wave equation:
    $$\\frac{\\partial^2 u}{\\partial t^2} = c^2 \\nabla^2 u$$
    describes wave propagation. Schrödinger's equation in quantum mechanics:
    \\begin{align}
    i\\hbar\\frac{\\partial \\Psi}{\\partial t} &= -\\frac{\\hbar^2}{2m}\\nabla^2\\Psi + V\\Psi \\\\
    \\text{or } \\hat{H}\\Psi &= i\\hbar\\frac{\\partial \\Psi}{\\partial t}
    \\end{align}
    Probability theory uses measure theory for rigorous foundations. The probability axioms state:
    $$P(\\Omega) = 1, \\quad P(A) \\geq 0, \\quad P\\left(\\bigcup_{i=1}^\\infty A_i\\right) = \\sum_{i=1}^\\infty P(A_i)$$
    for disjoint events. The expectation of a random variable is:
    \\[
    \\mathbb{E}[X] = \\int_{-\\infty}^{\\infty} x f_X(x) dx
    \\]
    for continuous variables, or:
    $$\\mathbb{E}[X] = \\sum_{x} x P(X=x)$$
    for discrete variables. The central limit theorem states that for independent identically distributed
    random variables with finite variance:
    \\(
    \\frac{1}{\\sqrt{n}} \\sum_{i=1}^n \\frac{X_i - \\mu}{\\sigma} \\xrightarrow{d} \\mathcal{N}(0,1)
    \\)
    Numerical analysis provides algorithms for computational mathematics. Newton's method for finding roots:
    $$x_{n+1} = x_n - \\frac{f(x_n)}{f'(x_n)}$$
    converges quadratically under suitable conditions. The Taylor series approximation:
    \\begin{equation}
    f(x) = \\sum_{n=0}^\\infty \\frac{f^{(n)}(a)}{n!} (x-a)^n
    \\end{equation}
    provides polynomial approximations. The Euler method for differential equations:
    $$y_{n+1} = y_n + h f(t_n, y_n)$$
    is a simple numerical integration scheme. More sophisticated methods include Runge-Kutta schemes:
    \\begin{align}
    k_1 &= h f(t_n, y_n) \\\\
    k_2 &= h f(t_n + \\frac{h}{2}, y_n + \\frac{k_1}{2}) \\\\
    k_3 &= h f(t_n + \\frac{h}{2}, y_n + \\frac{k_2}{2}) \\\\
    k_4 &= h f(t_n + h, y_n + k_3) \\\\
    y_{n+1} &= y_n + \\frac{1}{6}(k_1 + 2k_2 + 2k_3 + k_4)
    \\end{align}
    """
    
    # 执行保护
    protected, mapping = protector.protect(comprehensive_test_text)
    
    # 可视化结果
    visualize_protection_results(comprehensive_test_text, protected, mapping)
    
    # 验证恢复
    restored = protector.restore(protected, mapping)
    assert restored == comprehensive_test_text, "Restoration failed!"
    print("\n✓ All formulas successfully protected and restored!")
    
    return len(comprehensive_test_text.split()), len(mapping)

if __name__ == "__main__":
    # 运行单元测试
    print("Running unit tests...")
    unittest.main(argv=['first-arg-is-ignored'], exit=False, verbosity=2)
    
    # 运行综合测试并提供可视化报告
    print("\n" + "="*80)
    print("RUNNING COMPREHENSIVE PROTECTION TEST")
    print("="*80)
    word_count, formula_count = run_comprehensive_test()
    print(f"\nTest completed with {word_count} words and {formula_count} formulas protected.")