:root {
    --chatbot-color-light: #000000;
    --chatbot-color-dark: #FFFFFF;
    --chatbot-background-color-light: #F3F3F3;
    --chatbot-background-color-dark: #121111;
    --message-user-background-color-light: #95EC69;
    --message-user-background-color-dark: #26B561;
    --message-bot-background-color-light: #FFFFFF;
    --message-bot-background-color-dark: #2C2C2C;
}
mspace {
    display: block;
}
@media only screen and (max-width: 767px) {
  #column_1 {
    display: none !important;
  }
}
@keyframes highlight {
  0%, 100% {
    border: 2px solid transparent;
  }
  50% {
    border-color: yellow;
  }
}
.normal_mut_select .svelte-1gfkn6j {
    float: left;
    width: auto;
    line-height: 260% !important;
}
#highlight_update {
  animation-name: highlight;
  animation-duration: 0.75s;
  animation-iteration-count: 3;
}

.table-wrap.svelte-13hsdno.svelte-13hsdno.svelte-13hsdno {
    border: 0px solid var(--border-color-primary) !important;
}

#examples_col {
    z-index: 2;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    margin-bottom: 30% !important;
}
#hide_examples {
    z-index: 0;
}

#debug_mes {
    position: absolute;
    display: flex;
    bottom: 0;
    left: 0;
    z-index: 1; /* 设置更高的 z-index 值 */
    margin-bottom: -4px !important;
    align-self: flex-end;
}
#chat_box {
    display: flex;
    flex-direction: column;
    overflow-y: visible !important;
    z-index: 3;
    flex-grow: 1; /* 自动填充剩余空间 */
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    margin-bottom: 30px !important;
    border: 1px solid var(--border-color-primary);
}
.toast-body {
    z-index: 5 !important;
}
.chat_input {

}
.sm_btn {
    position: relative;
    bottom: 5px;
    height: 10%;
    border-radius: 20px!important;
    min-width: min(10%,100%) !important;
    overflow: hidden;
}
.sm_select {
    position: relative !important;
    z-index: 5 !important;
    bottom: 5px;
    min-width: min(20%,100%) !important;
    border-radius: 20px!important;
}
.sm_checkbox {
    position: relative !important;
    z-index: 5 !important;
    bottom: 5px;
    padding: 0 !important;
}
.sm_select .wrap-inner.svelte-aqlk7e.svelte-aqlk7e.svelte-aqlk7e {
    padding: 0 !important;
}
.sm_select .block.svelte-mppz8v {
    width: 10% !important;
}

button.sm {
    padding: 6px 8px !important;
}

/* usage_display */
.insert_block {
    position: relative;
    bottom: 2px;
    min-width: min(55px,100%) !important;
}

.submit_btn {
    flex-direction: column-reverse;
    overflow-y: auto !important;
    position: absolute;
    bottom: 0;
    right: 10px;
    margin-bottom: 10px !important;
    min-width: min(50px,100%) !important;
}

textarea {
    resize: none;
    height: 100%; /* 填充父元素的高度 */
}
/* #main_chatbot {
    height: 75vh !important;
    max-height: 75vh !important;
    overflow: auto !important;
    z-index: 2;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    will-change: transform !important;
} */
#prompt_result{
    height: 60vh !important;
    max-height: 60vh !important;
}

#app_title {
    font-weight: var(--prose-header-text-weight);
    font-size: var(--text-xxl);
    line-height: 1.3;
    text-align: left;
    margin-top: 6px;
    white-space: nowrap;
}
#description {
    text-align: center;
    margin: 32px 0 4px 0;
}

/* gradio的页脚信息 */
footer {
    /* display: none !important; */
    margin-top: .2em !important;
    font-size: 85%;
}
#footer {
    text-align: center;
}
#footer div {
    display: inline-block;
}
#footer .versions{
    font-size: 85%;
    opacity: 0.60;
}
/* user_info */

#float_display {
    position: absolute;
    max-height: 30px;
}
/* user_info */
#user_info {
    white-space: nowrap;
    position: absolute; left: 8em; top: .2em;
    z-index: var(--layer-2);
    box-shadow: var(--block-shadow);
    border: none; border-radius: var(--block-label-radius);
    background: var(--color-accent);
    padding: var(--block-label-padding);
    font-size: var(--block-label-text-size); line-height: var(--line-sm);
    width: auto; min-height: 30px !important;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}
textarea.svelte-1pie7s6 {
    background: #e7e6e6 !important;
    width: 100% !important;
}

.dark textarea.svelte-1pie7s6 {
    background: var(--input-background-fill) !important;
    width: 100% !important;
}

.dark input[type=number].svelte-1cl284s {
    background: #393939 !important;
    border: var(--input-border-width) solid var(--input-border-color) !important;
}
/* .dark input[type="range"] {
    background: #393939 !important;
} */
#user_info .wrap {
    opacity: 0;
}
#user_info p {
    color: white;
    font-weight: var(--block-label-text-weight);
}
#user_info.hideK {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}
[class *= "message"] {
    gap: 7px !important;
    border-radius: var(--radius-xl) !important
}
/* debug_mes */
#debug_mes {
    min-height: 2em;
    align-items: flex-end;
    justify-content: flex-end;
}
#debug_mes p {
    font-size: .85em;
    font-family: ui-monospace, "SF Mono", "SFMono-Regular", "Menlo", "Consolas", "Liberation Mono", "Microsoft Yahei UI", "Microsoft Yahei", monospace;
    /* Windows下中文的monospace会fallback为新宋体，实在太丑，这里折中使用微软雅黑 */
    color: #000000;
}
.dark #debug_mes p {
    color: #ee65ed;
}

#debug_mes {
    transition: all 0.6s;
}
#gpt-chatbot {
    transition: height 0.3s ease;
}

/* .wrap.svelte-18telvq.svelte-18telvq {
    padding: var(--block-padding) !important;
    height: 100% !important;
    max-height: 95% !important;
    overflow-y: auto !important;
}*/
/* .app.svelte-1mya07g.svelte-1mya07g {
    max-width: 100%;
    position: relative;
    padding: var(--size-4);
    width: 100%;
    height: 100%;
} */

.gradio-container-3-32-2 h1 {
    font-weight: 700 !important;
    font-size: 28px !important;
}


.gradio-container-3-32-2 h2 {
    font-weight: 600 !important;
    font-size: 24px !important;
}
.gradio-container-3-32-2 h3 {
    font-weight: 500 !important;
    font-size: 20px !important;
}
.gradio-container-3-32-2 h4 {
    font-weight: 400 !important;
    font-size: 16px !important;
}
.gradio-container-3-32-2 h5 {
    font-weight: 300 !important;
    font-size: 14px !important;
}
.gradio-container-3-32-2 h6 {
    font-weight: 200 !important;
    font-size: 12px !important;
}


#usage_display p, #usage_display span {
    margin: 0;
    font-size: .85em;
    color: var(--body-text-color-subdued);
}
.progress-bar {
    background-color: var(--input-background-fill);;
    margin: .5em 0 !important;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}
.progress {
    background-color: var(--block-title-background-fill);
    height: 100%;
    border-radius: 10px;
    text-align: right;
    transition: width 0.5s ease-in-out;
}
.progress-text {
    /* color: white; */
    color: var(--color-accent) !important;
    font-size: 1em !important;
    font-weight: bold;
    padding-right: 10px;
    line-height: 20px;
}

.apSwitch {
    top: 2px;
    display: inline-block;
    height: 24px;
    position: relative;
    width: 48px;
    border-radius: 12px;
}
.apSwitch input {
    display: none !important;
}
.apSlider {
    background-color: var(--neutral-200);
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    font-size: 18px;
    border-radius: 7px;
}
.apSlider::before {
    bottom: -1.5px;
    left: 1px;
    position: absolute;
    transition: .4s;
    content: "🌞";
}
hr.append-display {
    margin: 8px 0;
    border: none;
    height: 1px;
    border-top-width: 0;
    background-image: linear-gradient(to right, rgba(50,50,50, 0.1), rgba(150, 150, 150, 0.8), rgba(50,50,50, 0.1));
}
.source-a {
    font-size: 0.8em;
    max-width: 100%;
    margin: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    /* background-color: #dddddd88; */
    border-radius: 1.5rem;
    padding: 0.2em;
}
.source-a a {
    display: inline-block;
    background-color: #aaaaaa50;
    border-radius: 1rem;
    padding: 0.5em;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    min-width: 20%;
    white-space: nowrap;
    margin: 0.2rem 0.1rem;
    text-decoration: none !important;
    flex: 1;
    transition: flex 0.5s;
}
.source-a a:hover {
    background-color: #aaaaaa20;
    flex: 2;
}
input:checked + .apSlider {
    background-color: var(--primary-600);
}
input:checked + .apSlider::before {
    transform: translateX(23px);
    content:"🌚";
}

/* Override Slider Styles (for webkit browsers like Safari and Chrome)
 * 好希望这份提案能早日实现 https://github.com/w3c/csswg-drafts/issues/4410
 * 进度滑块在各个平台还是太不统一了
 */
input[type="range"] {
    -webkit-appearance: none;
    height: 4px;
    background: var(--input-background-fill);
    border-radius: 5px;
    background-image: linear-gradient(var(--primary-500),var(--primary-500));
    background-size: 0% 100%;
    background-repeat: no-repeat;
}
input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: solid 0.5px #ddd;
    background-color: white;
    cursor: ew-resize;
    box-shadow: var(--input-shadow);
    transition: background-color .1s ease;
}
input[type="range"]::-webkit-slider-thumb:hover {
    background: var(--neutral-50);
}
input[type="range"]::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
}

.submit_btn, #cancel_btn {
    height: 42px !important;
}
.submit_btn::before {
    content: url("data:image/svg+xml, %3Csvg width='21px' height='20px' viewBox='0 0 21 20' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E %3Cg id='page' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E %3Cg id='send' transform='translate(0.435849, 0.088463)' fill='%23FFFFFF' fill-rule='nonzero'%3E %3Cpath d='M0.579148261,0.0428666046 C0.301105539,-0.0961547561 -0.036517765,0.122307382 0.0032026237,0.420210298 L1.4927172,18.1553639 C1.5125774,18.4334066 1.79062012,18.5922882 2.04880264,18.4929872 L8.24518329,15.8913017 L11.6412765,19.7441794 C11.8597387,19.9825018 12.2370824,19.8832008 12.3165231,19.5852979 L13.9450591,13.4882182 L19.7839562,11.0255541 C20.0619989,10.8865327 20.0818591,10.4694687 19.7839562,10.3105871 L0.579148261,0.0428666046 Z M11.6138902,17.0883151 L9.85385903,14.7195502 L0.718169621,0.618812241 L12.69945,12.9346347 L11.6138902,17.0883151 Z' id='shape'%3E%3C/path%3E %3C/g%3E %3C/g%3E %3C/svg%3E");
    height: 21px;
}

#cancel_btn::before {
    content: url("data:image/svg+xml,%3Csvg width='21px' height='21px' viewBox='0 0 21 21' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E %3Cg id='pg' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E %3Cpath d='M10.2072007,20.088463 C11.5727865,20.088463 12.8594566,19.8259823 14.067211,19.3010209 C15.2749653,18.7760595 16.3386126,18.0538087 17.2581528,17.1342685 C18.177693,16.2147282 18.8982283,15.1527965 19.4197586,13.9484733 C19.9412889,12.7441501 20.202054,11.4557644 20.202054,10.0833163 C20.202054,8.71773046 19.9395733,7.43106036 19.4146119,6.22330603 C18.8896505,5.01555169 18.1673997,3.95018885 17.2478595,3.0272175 C16.3283192,2.10424615 15.2646719,1.3837109 14.0569176,0.865611739 C12.8491633,0.34751258 11.5624932,0.088463 10.1969073,0.088463 C8.83132146,0.088463 7.54636692,0.34751258 6.34204371,0.865611739 C5.1377205,1.3837109 4.07407321,2.10424615 3.15110186,3.0272175 C2.22813051,3.95018885 1.5058797,5.01555169 0.984349419,6.22330603 C0.46281914,7.43106036 0.202054,8.71773046 0.202054,10.0833163 C0.202054,11.4557644 0.4645347,12.7441501 0.9894961,13.9484733 C1.5144575,15.1527965 2.23670831,16.2147282 3.15624854,17.1342685 C4.07578877,18.0538087 5.1377205,18.7760595 6.34204371,19.3010209 C7.54636692,19.8259823 8.83475258,20.088463 10.2072007,20.088463 Z M10.2072007,18.2562448 C9.07493099,18.2562448 8.01471483,18.0452309 7.0265522,17.6232031 C6.03838956,17.2011753 5.17031614,16.6161693 4.42233192,15.8681851 C3.6743477,15.1202009 3.09105726,14.2521274 2.67246059,13.2639648 C2.25386392,12.2758022 2.04456558,11.215586 2.04456558,10.0833163 C2.04456558,8.95104663 2.25386392,7.89083047 2.67246059,6.90266784 C3.09105726,5.9145052 3.6743477,5.04643178 4.42233192,4.29844756 C5.17031614,3.55046334 6.036674,2.9671729 7.02140552,2.54857623 C8.00613703,2.12997956 9.06463763,1.92068122 10.1969073,1.92068122 C11.329177,1.92068122 12.3911087,2.12997956 13.3827025,2.54857623 C14.3742962,2.9671729 15.2440852,3.55046334 15.9920694,4.29844756 C16.7400537,5.04643178 17.3233441,5.9145052 17.7419408,6.90266784 C18.1605374,7.89083047 18.3698358,8.95104663 18.3698358,10.0833163 C18.3698358,11.215586 18.1605374,12.2758022 17.7419408,13.2639648 C17.3233441,14.2521274 16.7400537,15.1202009 15.9920694,15.8681851 C15.2440852,16.6161693 14.3760118,17.2011753 13.3878492,17.6232031 C12.3996865,18.0452309 11.3394704,18.2562448 10.2072007,18.2562448 Z M7.65444721,13.6242324 L12.7496608,13.6242324 C13.0584616,13.6242324 13.3003556,13.5384544 13.4753427,13.3668984 C13.6503299,13.1953424 13.7378234,12.9585951 13.7378234,12.6566565 L13.7378234,7.49968276 C13.7378234,7.19774418 13.6503299,6.96099688 13.4753427,6.78944087 C13.3003556,6.61788486 13.0584616,6.53210685 12.7496608,6.53210685 L7.65444721,6.53210685 C7.33878414,6.53210685 7.09345904,6.61788486 6.91847191,6.78944087 C6.74348478,6.96099688 6.65599121,7.19774418 6.65599121,7.49968276 L6.65599121,12.6566565 C6.65599121,12.9585951 6.74348478,13.1953424 6.91847191,13.3668984 C7.09345904,13.5384544 7.33878414,13.6242324 7.65444721,13.6242324 Z' id='shape' fill='%23FF3B30' fill-rule='nonzero'%3E%3C/path%3E %3C/g%3E %3C/svg%3E");
    height: 21px;
}
/* list */
ol:not(.options), ul:not(.options) {
    padding-inline-start: 2em !important;
}

/* 亮色（默认） */
#gpt-chatbot {
    background-color: var(--chatbot-background-color-light) !important;
    color: var(--chatbot-color-light) !important;
    box-shadow: 0 0 12px 4px rgba(0, 0, 0, 0.06);
}
/* 暗色 */
.dark #gpt-chatbot {
    background-color: var(--block-background-fill) !important;
    color: var(--chatbot-color-dark) !important;
    box-shadow: 0 0 12px 4px rgba(0, 0, 0, 0.2);
}

#gpt-panel > div {
    box-shadow: 0 0 12px 4px rgba(0, 0, 0, 0.06);
}
.dark #gpt-panel > div {
    box-shadow: 0 0 12px 4px rgba(0, 0, 0, 0.2);
}

/* 屏幕宽度大于等于500px的设备 */
/* update on 2023.4.8: 高度的细致调整已写入JavaScript */
/* @media screen and (min-width: 500px) {
    #main_chatbot {
        height: calc(100vh - 200px);
    }
    #main_chatbot .wrap {
        max-height: calc(100vh - 200px - var(--line-sm)*1rem - 2*var(--block-label-margin) );
    }
} */
/* 屏幕宽度小于500px的设备 */
/* @media screen and (max-width: 499px) {
    #main_chatbot {
        height: calc(100vh - 140px);
    }
    #main_chatbot .wrap {
        max-height: calc(100vh - 140px - var(--line-sm)*1rem - 2*var(--block-label-margin) );
    }
    [data-testid = "bot"] {
        max-width: 95% !important;
    }
    #app_title h1{
        letter-spacing: -1px; font-size: 22px;
    }
} */
#gpt-chatbot .wrap {
    overflow-x: hidden
}
/* 对话气泡 */
.message {
    border-radius: var(--radius-xl) !important;
    border: none;
    padding: var(--spacing-xl) !important;
    font-size: 15px !important;
    line-height: var(--line-md) !important;
    min-height: calc(var(--text-md)*var(--line-md) + 2*var(--spacing-xl));
    min-width: calc(var(--text-md)*var(--line-md) + 2*var(--spacing-xl));
}
[data-testid = "bot"] {
    max-width: 85%;
    border-bottom-left-radius: 0 !important;
    box-shadow: 2px 2px 0px 1px rgba(0, 0, 0, 0.06);
    background-color: var(--message-bot-background-color-light) !important;
}
[data-testid = "user"] {
    max-width: 85%;
    width: auto !important;
    border-bottom-right-radius: 0 !important;
    box-shadow: 2px 2px 0px 1px rgba(0, 0, 0, 0.06);
    background-color: var(--message-user-background-color-light) !important;
}
.dark [data-testid = "bot"] {
    background-color: var(--message-bot-background-color-dark) !important;
}
.dark [data-testid = "user"] {
    background-color: var(--message-user-background-color-dark) !important;
}

.message p {
    margin-top: 0.6em !important;
    margin-bottom: 0.6em !important;
}
.message p:first-child { margin-top: 0 !important; }
.message p:last-of-type { margin-bottom: 0 !important; }

.message .md-message {
    display: block;
    padding: 0 !important;
}
.message .raw-message {
    display: block;
    padding: 0 !important;
    white-space: pre-wrap;
}
.raw-message.hideM, .md-message.hideM {
    display: none;
}

/* custom buttons */
.chuanhu-btn {
    border-radius: 5px;
    /* background-color: #E6E6E6 !important; */
    color: rgba(120, 120, 120, 0.64) !important;
    padding: 4px !important;
    position: absolute;
    right: -22px;
    cursor: pointer !important;
    transition: color .2s ease, background-color .2s ease;
}
.chuanhu-btn:hover {
    background-color: rgba(167, 167, 167, 0.25) !important;
    color: unset !important;
}
.chuanhu-btn:active {
    background-color: rgba(167, 167, 167, 0.5) !important;
}
.chuanhu-btn:focus {
    outline: none;
}
.copy-bot-btn {
    /* top: 18px; */
    bottom: 0;
}
.toggle-md-btn {
    /* top: 0; */
    bottom: 20px;
}
.copy-code-btn {
    position: relative;
    float: right;
    font-size: 1em;
    cursor: pointer;
}

.message-wrap>div img{
    border-radius: 10px !important;
}

/* history message */
.wrap>.history-message {
    padding: 10px !important;
}
.history-message {
    /* padding: 0 !important; */
    opacity: 80%;
    display: flex;
    flex-direction: column;
}
.history-message>.history-message {
    padding: 0 !important;
}
.history-message>.message-wrap {
    padding: 0 !important;
    margin-bottom: 16px;
}
.history-message>.message {
    margin-bottom: 16px;
}
.wrap>.history-message::after {
    content: "";
    display: block;
    height: 2px;
    background-color: var(--body-text-color-subdued);
    margin-bottom: 10px;
    margin-top: -10px;
    clear: both;
}
.wrap>.history-message>:last-child::after {
    content: "仅供查看";
    display: block;
    text-align: center;
    color: var(--body-text-color-subdued);
    font-size: 0.8em;
}

/* 表格 */
table {
    margin: 1em 0;
    border-collapse: collapse;
    empty-cells: show;
}
td,th {
    border: 1.2px solid var(--border-color-primary) !important;
    padding: 0.2em;
}
thead {
    background-color: rgba(175,184,193,0.2);
}
thead th {
    padding: .5em .2em;
}
/* 行内代码 */
.message :not(pre) code {
    display: inline;
    white-space: break-spaces;
    border-radius: 6px;
    margin: 0 2px 0 2px;
    padding: .2em .4em .1em .4em;
    background-color: rgba(175,184,193,0.2);
}
/* 代码块 */
.message pre code {
    display: block;
    overflow: auto;
    white-space: pre;
    background-color: hsla(0, 0%, 7%, 70%)!important;
    border-radius: 10px;
    padding: 1.2em 1em 0em .5em;
    margin: 0.6em 2em 1em 0.2em;
    color: #FFF;
    box-shadow: 6px 6px 16px hsla(0, 0%, 0%, 0.2);
}
.dark .message pre code {
    background-color: hsla(0, 0%, 20%, 300%)!important;
}
.message pre {
    padding: 0 !important;
}
.message pre code div.highlight {
    background-color: unset !important;
}

button.copy-button {
    display: none;
}

/* 代码高亮样式 */
.codehilite .hll { background-color: #6e7681 }
.codehilite .c { color: #8b949e; font-style: italic } /* Comment */
.codehilite .err { color: #f85149 } /* Error */
.codehilite .esc { color: #c9d1d9 } /* Escape */
.codehilite .g { color: #c9d1d9 } /* Generic */
.codehilite .k { color: #ff7b72 } /* Keyword */
.codehilite .l { color: #a5d6ff } /* Literal */
.codehilite .n { color: #c9d1d9 } /* Name */
.codehilite .o { color: #ff7b72; font-weight: bold } /* Operator */
.codehilite .x { color: #c9d1d9 } /* Other */
.codehilite .p { color: #c9d1d9 } /* Punctuation */
.codehilite .ch { color: #8b949e; font-style: italic } /* Comment.Hashbang */
.codehilite .cm { color: #8b949e; font-style: italic } /* Comment.Multiline */
.codehilite .cp { color: #8b949e; font-weight: bold; font-style: italic } /* Comment.Preproc */
.codehilite .cpf { color: #8b949e; font-style: italic } /* Comment.PreprocFile */
.codehilite .c1 { color: #8b949e; font-style: italic } /* Comment.Single */
.codehilite .cs { color: #8b949e; font-weight: bold; font-style: italic } /* Comment.Special */
.codehilite .gd { color: #ffa198; background-color: #490202 } /* Generic.Deleted */
.codehilite .ge { color: #c9d1d9; font-style: italic } /* Generic.Emph */
.codehilite .gr { color: #ffa198 } /* Generic.Error */
.codehilite .gh { color: #79c0ff; font-weight: bold } /* Generic.Heading */
.codehilite .gi { color: #56d364; background-color: #0f5323 } /* Generic.Inserted */
.codehilite .go { color: #8b949e } /* Generic.Output */
.codehilite .gp { color: #8b949e } /* Generic.Prompt */
.codehilite .gs { color: #c9d1d9; font-weight: bold } /* Generic.Strong */
.codehilite .gu { color: #79c0ff } /* Generic.Subheading */
.codehilite .gt { color: #ff7b72 } /* Generic.Traceback */
.codehilite .g-Underline { color: #c9d1d9; text-decoration: underline } /* Generic.Underline */
.codehilite .kc { color: #79c0ff } /* Keyword.Constant */
.codehilite .kd { color: #ff7b72 } /* Keyword.Declaration */
.codehilite .kn { color: #ff7b72 } /* Keyword.Namespace */
.codehilite .kp { color: #79c0ff } /* Keyword.Pseudo */
.codehilite .kr { color: #ff7b72 } /* Keyword.Reserved */
.codehilite .kt { color: #ff7b72 } /* Keyword.Type */
.codehilite .ld { color: #79c0ff } /* Literal.Date */
.codehilite .m { color: #a5d6ff } /* Literal.Number */
.codehilite .s { color: #a5d6ff } /* Literal.String */
.codehilite .na { color: #c9d1d9 } /* Name.Attribute */
.codehilite .nb { color: #c9d1d9 } /* Name.Builtin */
.codehilite .nc { color: #f0883e; font-weight: bold } /* Name.Class */
.codehilite .no { color: #79c0ff; font-weight: bold } /* Name.Constant */
.codehilite .nd { color: #d2a8ff; font-weight: bold } /* Name.Decorator */
.codehilite .ni { color: #ffa657 } /* Name.Entity */
.codehilite .ne { color: #f0883e; font-weight: bold } /* Name.Exception */
.codehilite .nf { color: #d2a8ff; font-weight: bold } /* Name.Function */
.codehilite .nl { color: #79c0ff; font-weight: bold } /* Name.Label */
.codehilite .nn { color: #ff7b72 } /* Name.Namespace */
.codehilite .nx { color: #c9d1d9 } /* Name.Other */
.codehilite .py { color: #79c0ff } /* Name.Property */
.codehilite .nt { color: #7ee787 } /* Name.Tag */
.codehilite .nv { color: #79c0ff } /* Name.Variable */
.codehilite .ow { color: #ff7b72; font-weight: bold } /* Operator.Word */
.codehilite .pm { color: #c9d1d9 } /* Punctuation.Marker */
.codehilite .w { color: #6e7681 } /* Text.Whitespace */
.codehilite .mb { color: #a5d6ff } /* Literal.Number.Bin */
.codehilite .mf { color: #a5d6ff } /* Literal.Number.Float */
.codehilite .mh { color: #a5d6ff } /* Literal.Number.Hex */
.codehilite .mi { color: #a5d6ff } /* Literal.Number.Integer */
.codehilite .mo { color: #a5d6ff } /* Literal.Number.Oct */
.codehilite .sa { color: #79c0ff } /* Literal.String.Affix */
.codehilite .sb { color: #a5d6ff } /* Literal.String.Backtick */
.codehilite .sc { color: #a5d6ff } /* Literal.String.Char */
.codehilite .dl { color: #79c0ff } /* Literal.String.Delimiter */
.codehilite .sd { color: #a5d6ff } /* Literal.String.Doc */
.codehilite .s2 { color: #a5d6ff } /* Literal.String.Double */
.codehilite .se { color: #79c0ff } /* Literal.String.Escape */
.codehilite .sh { color: #79c0ff } /* Literal.String.Heredoc */
.codehilite .si { color: #a5d6ff } /* Literal.String.Interpol */
.codehilite .sx { color: #a5d6ff } /* Literal.String.Other */
.codehilite .sr { color: #79c0ff } /* Literal.String.Regex */
.codehilite .s1 { color: #a5d6ff } /* Literal.String.Single */
.codehilite .ss { color: #a5d6ff } /* Literal.String.Symbol */
.codehilite .bp { color: #c9d1d9 } /* Name.Builtin.Pseudo */
.codehilite .fm { color: #d2a8ff; font-weight: bold } /* Name.Function.Magic */
.codehilite .vc { color: #79c0ff } /* Name.Variable.Class */
.codehilite .vg { color: #79c0ff } /* Name.Variable.Global */
.codehilite .vi { color: #79c0ff } /* Name.Variable.Instance */
.codehilite .vm { color: #79c0ff } /* Name.Variable.Magic */
.codehilite .il { color: #a5d6ff } /* Literal.Number.Integer.Long */

.dark .codehilite .hll { background-color: #2C3B41 }
.dark .codehilite .c { color: #79d618; font-style: italic } /* Comment */
.dark .codehilite .err { color: #FF5370 } /* Error */
.dark .codehilite .esc { color: #89DDFF } /* Escape */
.dark .codehilite .g { color: #EEFFFF } /* Generic */
.dark .codehilite .k { color: #BB80B3 } /* Keyword */
.dark .codehilite .l { color: #C3E88D } /* Literal */
.dark .codehilite .n { color: #EEFFFF } /* Name */
.dark .codehilite .o { color: #89DDFF } /* Operator */
.dark .codehilite .p { color: #89DDFF } /* Punctuation */
.dark .codehilite .ch { color: #79d618; font-style: italic } /* Comment.Hashbang */
.dark .codehilite .cm { color: #79d618; font-style: italic } /* Comment.Multiline */
.dark .codehilite .cp { color: #79d618; font-style: italic } /* Comment.Preproc */
.dark .codehilite .cpf { color: #79d618; font-style: italic } /* Comment.PreprocFile */
.dark .codehilite .c1 { color: #79d618; font-style: italic } /* Comment.Single */
.dark .codehilite .cs { color: #79d618; font-style: italic } /* Comment.Special */
.dark .codehilite .gd { color: #FF5370 } /* Generic.Deleted */
.dark .codehilite .ge { color: #89DDFF } /* Generic.Emph */
.dark .codehilite .gr { color: #FF5370 } /* Generic.Error */
.dark .codehilite .gh { color: #C3E88D } /* Generic.Heading */
.dark .codehilite .gi { color: #C3E88D } /* Generic.Inserted */
.dark .codehilite .go { color: #79d618 } /* Generic.Output */
.dark .codehilite .gp { color: #FFCB6B } /* Generic.Prompt */
.dark .codehilite .gs { color: #FF5370 } /* Generic.Strong */
.dark .codehilite .gu { color: #89DDFF } /* Generic.Subheading */
.dark .codehilite .gt { color: #FF5370 } /* Generic.Traceback */
.dark .codehilite .kc { color: #89DDFF } /* Keyword.Constant */
.dark .codehilite .kd { color: #BB80B3 } /* Keyword.Declaration */
.dark .codehilite .kn { color: #89DDFF; font-style: italic } /* Keyword.Namespace */
.dark .codehilite .kp { color: #89DDFF } /* Keyword.Pseudo */
.dark .codehilite .kr { color: #BB80B3 } /* Keyword.Reserved */
.dark .codehilite .kt { color: #BB80B3 } /* Keyword.Type */
.dark .codehilite .ld { color: #C3E88D } /* Literal.Date */
.dark .codehilite .m { color: #F78C6C } /* Literal.Number */
.dark .codehilite .s { color: #C3E88D } /* Literal.String */
.dark .codehilite .na { color: #BB80B3 } /* Name.Attribute */
.dark .codehilite .nb { color: #82AAFF } /* Name.Builtin */
.dark .codehilite .nc { color: #FFCB6B } /* Name.Class */
.dark .codehilite .no { color: #EEFFFF } /* Name.Constant */
.dark .codehilite .nd { color: #82AAFF } /* Name.Decorator */
.dark .codehilite .ni { color: #89DDFF } /* Name.Entity */
.dark .codehilite .ne { color: #FFCB6B } /* Name.Exception */
.dark .codehilite .nf { color: #82AAFF } /* Name.Function */
.dark .codehilite .nl { color: #82AAFF } /* Name.Label */
.dark .codehilite .nn { color: #FFCB6B } /* Name.Namespace */
.dark .codehilite .nx { color: #EEFFFF } /* Name.Other */
.dark .codehilite .py { color: #FFCB6B } /* Name.Property */
.dark .codehilite .nt { color: #FF5370 } /* Name.Tag */
.dark .codehilite .nv { color: #89DDFF } /* Name.Variable */
.dark .codehilite .ow { color: #89DDFF; font-style: italic } /* Operator.Word */
.dark .codehilite .pm { color: #89DDFF } /* Punctuation.Marker */
.dark .codehilite .w { color: #EEFFFF } /* Text.Whitespace */
.dark .codehilite .mb { color: #F78C6C } /* Literal.Number.Bin */
.dark .codehilite .mf { color: #F78C6C } /* Literal.Number.Float */
.dark .codehilite .mh { color: #F78C6C } /* Literal.Number.Hex */
.dark .codehilite .mi { color: #F78C6C } /* Literal.Number.Integer */
.dark .codehilite .mo { color: #F78C6C } /* Literal.Number.Oct */
.dark .codehilite .sa { color: #BB80B3 } /* Literal.String.Affix */
.dark .codehilite .sb { color: #C3E88D } /* Literal.String.Backtick */
.dark .codehilite .sc { color: #C3E88D } /* Literal.String.Char */
.dark .codehilite .dl { color: #EEFFFF } /* Literal.String.Delimiter */
.dark .codehilite .sd { color: #79d618; font-style: italic } /* Literal.String.Doc */
.dark .codehilite .s2 { color: #C3E88D } /* Literal.String.Double */
.dark .codehilite .se { color: #EEFFFF } /* Literal.String.Escape */
.dark .codehilite .sh { color: #C3E88D } /* Literal.String.Heredoc */
.dark .codehilite .si { color: #89DDFF } /* Literal.String.Interpol */
.dark .codehilite .sx { color: #C3E88D } /* Literal.String.Other */
.dark .codehilite .sr { color: #89DDFF } /* Literal.String.Regex */
.dark .codehilite .s1 { color: #C3E88D } /* Literal.String.Single */
.dark .codehilite .ss { color: #89DDFF } /* Literal.String.Symbol */
.dark .codehilite .bp { color: #89DDFF } /* Name.Builtin.Pseudo */
.dark .codehilite .fm { color: #82AAFF } /* Name.Function.Magic */
.dark .codehilite .vc { color: #89DDFF } /* Name.Variable.Class */
.dark .codehilite .vg { color: #89DDFF } /* Name.Variable.Global */
.dark .codehilite .vi { color: #89DDFF } /* Name.Variable.Instance */
.dark .codehilite .vm { color: #82AAFF } /* Name.Variable.Magic */
.dark .codehilite .il { color: #F78C6C } /* Literal.Number.Integer.Long */
