import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到sys.path，以便正确导入模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 使用mock处理依赖项
sys.modules['toolbox'] = MagicMock()
sys.modules['marko'] = MagicMock()
sys.modules['marko.block'] = MagicMock()

# 现在可以导入模块了
from gpt_academic.crazy_functions.Markdown_Translate_Preoutline import (
    extract_outline, 
    compress_outline, 
    get_document_outline,
    get_book_outline,
    enhance_translation_prompt_with_book_outline,
    extract_text_from_node
)

class TestMarkdownOutline(unittest.TestCase):
    
    def setUp(self):
        """创建测试用的Markdown内容"""
        # 创建一个包含丰富内容的大纲，大约2500个字符
        self.large_outline_content = """# 第一章 简介与概述

## 1.1 研究背景

本研究的背景源于现代信息处理技术的快速发展。

## 1.2 研究意义

### 1.2.1 理论意义

深入探讨相关理论基础。

### 1.2.2 实践意义

在实际应用中具有重要价值。

## 1.3 研究目标与内容

主要研究目标包括以下几个方面。

## 1.4 研究方法与技术路线

采用多种研究方法相结合的方式。

## 1.5 论文结构安排

### 第一部分：绪论

介绍研究背景和意义。

### 第二部分：文献综述

回顾相关领域研究成果。

### 第三部分：理论分析

构建理论模型。

### 第四部分：实证研究

通过实证验证理论。

### 第五部分：结论与展望

总结研究成果。

# 第二章 文献综述

## 2.1 国外研究现状

### 2.1.1 早期研究成果

早期的研究奠定了基础。

### 2.1.2 近期研究进展

近期研究在多个方面取得突破。

## 2.2 国内研究现状

### 2.2.1 理论研究

国内理论研究逐步深入。

### 2.2.2 应用研究

应用研究取得显著成果。

## 2.3 研究评述

### 2.3.1 研究贡献

已有研究的主要贡献。

### 2.3.2 研究不足

存在的不足和局限性。

# 第三章 理论基础与分析框架

## 3.1 核心概念界定

### 3.1.1 基本概念

对基本概念进行界定。

### 3.1.2 相关概念

区分相关概念。

## 3.2 理论基础

### 3.2.1 理论来源

理论的来源和发展。

### 3.2.2 理论内涵

理论的核心内容。

## 3.3 分析框架构建

### 3.3.1 框架设计原则

设计框架的基本原则。

### 3.3.2 框架结构

框架的具体结构。

# 第四章 研究设计与方法

## 4.1 研究范式

### 4.1.1 研究哲学

研究的哲学基础。

### 4.1.2 研究策略

采用的研究策略。

## 4.2 数据收集方法

### 4.2.1 定量数据收集

定量数据的收集方式。

### 4.2.2 定性数据收集

定性数据的收集方式。

## 4.3 数据分析方法

### 4.3.1 定量分析方法

定量数据的分析方法。

### 4.3.2 定性分析方法

定性数据的分析方法。

# 第五章 实证分析与结果

## 5.1 数据描述与预处理

### 5.1.1 数据来源

数据的获取途径。

### 5.1.2 数据预处理

数据预处理的方法。

## 5.2 描述性统计分析

### 5.2.1 基本统计特征

数据的基本统计特征。

### 5.2.2 分布特征分析

数据的分布特征。

## 5.3 实证结果分析

### 5.3.1 假设检验

对研究假设进行检验。

### 5.3.2 结果讨论

对结果进行深入讨论。

# 第六章 结论与建议

## 6.1 研究结论

### 6.1.1 主要发现

研究的主要发现。

### 6.1.2 理论贡献

对理论的贡献。

## 6.2 对策建议

### 6.2.1 实践建议

对实践的建议。

### 6.2.2 政策建议

对政策制定的建议。

## 6.3 研究局限与展望

### 6.3.1 研究局限性

研究存在的局限性。

### 6.3.2 未来研究方向

未来可能的研究方向。

## 参考文献

## 附录

### 附录A 调查问卷

### 附录B 访谈提纲

### 附录C 数据表格
"""
        
        # 确保内容长度约为2500字符
        print(f"测试内容长度: {len(self.large_outline_content)} 字符")
        
    def test_extract_text_from_node(self):
        """测试从节点提取文本功能"""
        # 测试简单文本节点
        result = extract_text_from_node("简单文本")
        self.assertEqual(result, "简单文本")
        
        # 测试带HTML标记的文本
        result = extract_text_from_node("<mark>标记文本</mark>")
        self.assertEqual(result, "标记文本")
        
        print(f"文本提取测试完成")
        
    def test_compress_outline_without_secondary(self):
        """测试大纲压缩功能（不使用二级补充）"""
        # 创建模拟的大纲结构
        outline = [
            {'level': 1, 'title': '第一章 简介', 'position': (1, 1)},
            {'level': 2, 'title': '1.1 背景', 'position': (2, 2)},
            {'level': 2, 'title': '1.2 意义', 'position': (3, 3)},
            {'level': 3, 'title': '1.2.1 理论意义', 'position': (4, 4)},
            {'level': 3, 'title': '1.2.2 实践意义', 'position': (5, 5)},
            {'level': 1, 'title': '第二章 文献综述', 'position': (6, 6)},
            {'level': 2, 'title': '2.1 国外研究', 'position': (7, 7)},
        ]
        
        # 测试默认压缩（1000字符限制）
        compressed = compress_outline(outline, max_chars=1000, secondary_max_chars=0)
        
        self.assertIsInstance(compressed, str)
        self.assertIn("第一章 简介", compressed)
        self.assertIn("第二章 文献综述", compressed)
        
        print(f"压缩后长度: {len(compressed)} 字符")
        print(f"压缩后内容预览: {compressed[:200]}...")
        
    def test_compress_outline_with_secondary(self):
        """测试大纲压缩功能（使用二级补充）"""
        # 创建一个较大的模拟大纲结构，确保触发二级补充机制
        outline = []
        # 创建足够多的标题项，确保总长度超过基础限制
        for i in range(30):
            outline.append({'level': 1, 'title': f'第{i+1}章 标题内容测试，这是一段比较长的标题内容用于测试字符限制功能', 'position': (i*3, i*3+1)})
            for j in range(3):
                outline.append({'level': 2, 'title': f'{i+1}.{j+1} 节内容测试，这也是一段比较长的小节标题内容', 'position': (i*3+j, i*3+j+1)})
                for k in range(2):
                    outline.append({'level': 3, 'title': f'{i+1}.{j+1}.{k+1} 小节内容测试，这同样是一段比较长的子小节标题内容', 'position': (i*3+j+k, i*3+j+k+1)})
        
        # 设置较小的基础限制和二级补充限制，确保触发二级补充机制
        base_limit = 500
        secondary_limit = 200
        compressed = compress_outline(outline, max_chars=base_limit, secondary_max_chars=secondary_limit)
        
        self.assertIsInstance(compressed, str)
        # 验证确实包含了二级补充内容（长度应大于基础限制但不超过基础限制+二级限制）
        self.assertGreater(len(compressed), base_limit)
        self.assertLessEqual(len(compressed), base_limit + secondary_limit + 100)  # 给一些额外空间用于格式字符
        
        print(f"带二级补充压缩后长度: {len(compressed)} 字符")
        print(f"压缩后内容预览: {compressed[:300]}...")
        
    @patch('gpt_academic.crazy_functions.Markdown_Translate_Preoutline.extract_outline')
    @patch('gpt_academic.crazy_functions.Markdown_Translate_Preoutline.compress_outline')
    def test_get_document_outline(self, mock_compress_outline, mock_extract_outline):
        """测试从文件获取大纲功能"""
        # 设置mock返回值
        mock_extract_outline.return_value = (
            [{'level': 1, 'title': '测试标题', 'position': (1, 1)}], 
            'regex'
        )
        mock_compress_outline.return_value = "# 测试标题\n\n"
        
        # 创建临时文件进行测试
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write("# 测试标题\n\n测试内容")
            temp_file_path = f.name
            
        try:
            # 测试获取文档大纲
            outline_text, method = get_document_outline(temp_file_path, secondary_max_chars=200)
            
            self.assertIsInstance(outline_text, str)
            self.assertIsInstance(method, str)
            self.assertGreater(len(outline_text), 0)
            
            print(f"从文件提取大纲长度: {len(outline_text)} 字符")
            print(f"提取方法: {method}")
            print(f"大纲内容预览: {outline_text[:200]}...")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
            
    @patch('gpt_academic.crazy_functions.Markdown_Translate_Preoutline.get_document_outline')
    def test_get_book_outline(self, mock_get_document_outline):
        """测试获取书籍大纲功能"""
        # 设置mock返回值
        mock_get_document_outline.return_value = (
            "# 第一章 简介\n## 1.1 背景\n## 1.2 意义", 
            'regex'
        )
        
        # 创建临时目录和文件进行测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建full.md文件
            full_md_path = os.path.join(temp_dir, 'full.md')
            with open(full_md_path, 'w', encoding='utf-8') as f:
                f.write("# 测试内容")
                
            # 测试获取书籍大纲
            book_outline, method = get_book_outline(temp_dir, max_outline_chars=1000, secondary_max_chars=200)
            
            self.assertIsInstance(book_outline, str)
            self.assertIsInstance(method, str)
            self.assertGreater(len(book_outline), 0)
            
            print(f"书籍大纲长度: {len(book_outline)} 字符")
            print(f"提取方法: {method}")
            print(f"书籍大纲预览: {book_outline[:300]}...")
            
    @patch('gpt_academic.crazy_functions.Markdown_Translate_Preoutline.get_book_outline')
    def test_enhance_translation_prompt_with_book_outline(self, mock_get_book_outline):
        """测试使用书籍大纲增强翻译提示词功能"""
        # 设置mock返回值
        mock_get_book_outline.return_value = (
            "# 第一章 简介\n## 1.1 背景\n## 1.2 意义", 
            'regex'
        )
        
        original_prompt = "请将以下内容翻译成中文，保持学术性和准确性。"
        
        # 创建临时目录进行测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试增强翻译提示词
            enhanced_prompt = enhance_translation_prompt_with_book_outline(
                original_prompt, 
                temp_dir, 
                max_outline_chars=1000, 
                secondary_max_chars=200
            )
            
            self.assertIsInstance(enhanced_prompt, str)
            self.assertGreater(len(enhanced_prompt), len(original_prompt))
            self.assertIn("书籍大纲信息", enhanced_prompt)
            self.assertIn(original_prompt, enhanced_prompt)
            
            print(f"原始提示词长度: {len(original_prompt)} 字符")
            print(f"增强后提示词长度: {len(enhanced_prompt)} 字符")
            print(f"增强后提示词预览: {enhanced_prompt[:500]}...")

if __name__ == '__main__':
    unittest.main()