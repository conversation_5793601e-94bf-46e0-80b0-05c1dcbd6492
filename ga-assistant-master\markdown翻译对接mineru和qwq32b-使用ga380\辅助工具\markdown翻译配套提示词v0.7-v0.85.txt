# V0.85
新增：继续加强0.84版得断句死角

~~~
(prompt)
v0.85
一、翻译基础要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。
数学公式：一个一个的，一句一句的翻译公式，尽量隔离公式之间的错误。

【特别重要】独立行的LaTeX数学公式处理：
1. 独立行的数学公式是指单独占一行且被$$...$$包围的公式，不需要翻译，直接原样保留
2. 所有$$...$$格式的独立行公式必须保持原样，一字不改地复制到译文中
3. 行内公式（即被单个$符号包围的公式）仍然按照原有方式处理

二、写作技巧（针对非代码和非数学公式段落）：
1、不影响内容和逻辑时，在文章中适当留白，让读者有思考的空间
2、在叙述中分层次表达情感，让情感表达更丰富
3、略微加入幽默元素，让文章更轻松有趣
4、句式要长短结合，徐徐的切换，使得文章不那么生硬。
5、用更简洁的方式传递信息，让句子更加有力
6、删除多余的词语，使表达更加简练
7、把复杂的句子拆开，用短句子表达，更有冲击力
8、用更多动词，让文章更有活力
9、简洁的语言表达复杂的思想，使文章更精炼有力
10、尽量用主动句，增加文章力量
11、全部使用本土句式代替原有句法，不必考虑句式的对应关系。但因果机制要严谨。
12、译文要引发情绪共鸣，增强代入感,但要保证意义等价。全文情感要强烈同时保持一定克制，文笔强烈但不过度夸张。
13、通过双向传递情感，让读者在情感交流中受到感染，保持因果机制要严谨
14、通过句式的长短搭配，使文章读起来有节奏感
15、通过长短句的变化，控制文章的节奏
16、多维度润化译文，增加可读性。但保持不越界。
17、断句要明确，译文中长句子的容忍度为0。断句的优先级是最高的。长句子通通强行拆解为短句的组合，阅读时候要体验出断句的顿挫感，这是简化阅读强度的关键！！
18、量化数据视觉强化
19、情景具象化

三、详细断句规则（针对非代码和非数学公式段落）：
1、用破折号分层，对比句前置，术语精简。
2、冒号引导递进，拆分定中结构，转折限行化。
3、动宾紧凑衔接，逗号强调主语
4、情景状语前置，冒号列举式，
5、动词强化
6、场景具象化
7、精确量化
8、短句点睛
9、比喻显性化
10、14字以上用逗号等分割
11、逗号激活比喻
12、补转折词
13、前置时间；焦点前置
14、动词阶梯递进。
15、多重动词要分割。
16、用破折号显性归因。

对比举例第一批：以下是调整后的表格（已去除模型名称，仅保留版本号）：

| 最佳断句案例 (满分)                                      | 最差断句案例 (缺陷)                                  | 核心差异点                      |
| ------------------------------------------------ | -------------------------------------------- | -------------------------- |
| 近年发现的坏死性凋亡——这种受控坏死形式依赖于RIP1/RIP3蛋白。        | 坏死性凋亡被描述为一种依赖RIP1和/或RIP3的受调控坏死形式。      | ✅ 破折号分层 vs ❌ 30字无停顿长句      |
| 与传统坏死不同，坏死性凋亡的典型特征在于分子事件的有序性。              | 分子事件的控制序列是坏死性凋亡的典型特征。                  | ✅ 对比前置 vs ❌ 英文语序直译         |
| 凋亡通过DNA核小体间断裂及半胱天冬酶激活等特征定义。                | 凋亡通过被称为半胱天冬酶（caspases）的细胞内酶的激活来定义。     | ✅ 术语精简 vs ❌ 22字超长插入语       |
| 研究发现：细胞代谢状态不仅决定细胞生存与否，更能精准调控不同死亡模式的启动。    | 细胞代谢对程序性细胞死亡的调控具有深远影响，尤其对死亡类型具有决定性作用。 | ✅ 冒号引导递进 vs ❌ "尤其"逻辑断裂     |
| 深入解析代谢波动如何通过信号转导通路精准调控不同细胞死亡路径。           | 深入解析代谢波动调控不同细胞死亡通路的信号传导级联反应...        | ✅ 拆分定中结构 vs ❌ 16字超长定语      |
| 值得注意的是，代谢异常与细胞死亡通路失调不仅是肿瘤核心特征，同样参与多种人类疾病。 | 由于代谢失调与细胞死亡通路改变还参与了多种人类疾病的发病机制...      | ✅ 转折显性化 vs ❌ "由于"缺失结果句     |
| 精准解析控制死亡模式的分子事件，对选择性干预这些过程至关重要。            | 由于这些不同形式的程序性细胞死亡在正常和病理条件下均参与重要调控...    | ✅ 动宾紧凑衔接 vs ❌ 连词滥用无重点      |
| 本文特别聚焦于代谢与坏死性凋亡之间的关联机制。                   | 本篇综述将聚焦代谢与程序性坏死的关联机制展开探讨。             | ✅ 主动态单句 vs ❌ "聚焦...展开探讨"重复 |
| 癌细胞的代谢特征图谱，势必对恶性转化的各个阶段产生深远影响。             | 这一知识在医学多个领域具有巨大的转化潜力。                 | ✅ 逗号强调主语 vs ❌ 术语孤立无上下文     |
对比举例第二批：

| **原句核心内容**      | **最佳断句案例**                                        | **最差断句案例**                                    | **优化核心**                               |
| --------------- | ------------------------------------------------- | --------------------------------------------- | -------------------------------------- |
| **信号通路与代谢交汇**   | 细胞死亡信号通路与代谢事件的**深度交织**，正推动治疗新策略的诞生。        | 这种细胞死亡信号通路和代谢事件之间的交叉互动正在被研究用于开发新的治疗方法。 (v0.6) | ✅动词动态化("推动") vs ❌被动化("正在被研究")          |
| **代谢波动响应机制**    | 当**葡萄糖供应骤减**时，细胞通过激活AMPK通路**重编程代谢流向**。     | 细胞在葡萄糖供应减少的情况下通过AMPK通路激活进行代谢重编程。        | ✅情景状语前置 vs ❌30字无停顿长句                   |
| **凋亡的生化标志定义**   | 凋亡的判定需满足**三大标志**：染色质凝集、caspase级联激活、凋亡小体形成。  | 凋亡通过DNA核小体间断裂和被称为半胱天冬酶的蛋白酶激活等参数定义。 (v0.6)     | ✅冒号列举式 vs ❌超长定中结构("被称为...的蛋白酶")        |
| **转化医学潜力说明**    | 这些发现**不仅解锁了肿瘤治疗新靶点**，更为代谢性疾病提供干预思路。        | 由于该机制在多种疾病中发挥作用，因此具有转化潜力。             | ✅动词强化("解锁") vs ❌笼统表述("发挥作用")           |
| **坏死性凋亡的病理意义**  | **炎症风暴中**，坏死性凋亡通过释放DAMPs分子**放大组织损伤**。     | 坏死性凋亡在炎症条件下会造成损伤相关分子模式的释放从而导致组织损伤。 (v0.6)     | ✅场景具象化("炎症风暴") vs ❌术语堆砌无断句             |
| **代谢决定死亡类型的机制** | 线粒体代谢产物乙酰-CoA**累积至临界浓度时**，将直接**触发凋亡程序**。    | 当线粒体代谢产物乙酰辅酶A积累到一定水平会诱导细胞凋亡。          | ✅精确量化("临界浓度") vs ❌模糊表述("一定水平")         |
| **分子事件的有序性强调**  | **关键在有序**：坏死性凋亡严格遵循RIP1-RIP3-MLKL磷酸化级联。    | 坏死性凋亡的典型特征是其分子事件序列受到严格调控。               | ✅短句点睛("关键在有序") vs ❌抽象描述("受到严格调控")      |
| **代谢干预的治疗前景**   | 靶向谷氨酰胺代谢**如同切断敌军粮草**，可有效增敏肿瘤细胞死亡。          | 通过抑制谷氨酰胺代谢途径可以增强肿瘤细胞对死亡的敏感性。           | ✅军事比喻显性化 vs ❌机械直译("通过抑制...途径")         |
| **基础研究的临床价值**   | 这些分子机制的解析，**正为"不可成药"靶点带来破局曙光**。            | 对该机制的深入理解可能为难以靶向治疗的靶点提供新思路。           | ✅创新术语("不可成药") + 动态意象("破局曙光") vs ❌模板化表达 |

| 很差断句                         | 断句问题类型 | 具体缺陷                 | 正确方法       | 正确断句                       |
| ---------------------------------- | ------ | -------------------- | ---------- | --------------------------- |
| 在电离辐射冲击下原子结构重组导致性质不可预测改变           | 科技长句窒息 | 14字无逗号，科技术语粘连        | 在"冲击下"后增逗号 | 在电离辐射冲击下，原子结构重组性质不可控改变      |
| 反复小剂量投药逐渐耗尽乙酰胆碱酯酶储备                | 专业术语梗阻 | 9字专业名词“乙酰胆碱酯酶”未分段    | 拆分为        | 反复微量投药，乙酰胆碱酯酶储备逐渐枯竭         |
| 五氯酚阻断能量代谢链致受害者如同自燃                 | 比喻黏连   | 动作("致")与比喻("自燃")直接黏连 | 增逗号激活比喻    | 五氯酚阻断能量链，受害者如同自燃            |
| 加州公共卫生部声称未发现危害仍下令停用DDD             | 逻辑断层   | "声称"与"下令"因果断裂        | 补转折词       | 加州卫生部虽称无害，仍紧急叫停DDD          |
| 被农田包围的保护区孤岛般共享污染水源                 | 定语吞噬   | 12字定语压缩主语("保护区")     | 破开定语       | 保护区困于农田，如孤岛共饮毒水             |
| 最后一次施药后9个月孵化的新鱼携带毒素                | 时间定语超载 | 9字时间状语压迫主语("新鱼")     | 前置时间       | 停药九月后，新孵化鱼群竟携带毒素            |
| 美狄亚魔袍化作化学药剂在血液埋死亡引信                | 主谓失衡   | 次要信息("在血液")抢夺焦点      | 焦点前置       | 美狄亚魔袍变毒剂，死亡引信藏血脉            |
| 蓄意投毒入水库竟成常态居民唯饮毒或付费                | 制度批判模糊 | 未突出政策荒诞性             | 增破折号       | 投毒入水库竟常态——居民抉择：饮鸩或买命？       |
| 浮游生物含毒5ppm植食鱼蓄40-300ppm肉食鱼达2500ppm | 数据平铺   | 递进感缺失                | 增动词阶梯      | 浮游蓄毒5ppm→植食鱼翻80倍→肉食鱼爆增500倍！ |
| 新生雏鸟破壳数小时入水藏亲鸟羽翼下                  | 动态黏连   | 三重动作未分层              | 时间切割       | 雏鸟破壳，数小时入水，藏亲鸟羽翼下           |

| 很差断句                                                                        | 关键缺陷        | 正确断句                                          | 正确方法                      |
| --------------------------------------------------------------------------- | ----------- | --------------------------------------------- | ------------------------- |
| ​**​“人类首次在地球历史上，每个人都从受孕到死亡，始终暴露在危险化学物质的威胁中。”​**​                            | 时间状语割裂主谓    | “人类首度——从受孕到死亡——被化学威胁全程笼罩。”                    | 破折号封装状语链，强化“笼罩”动词         |
| ​**​“这种污染大多不可逆转——它不仅破坏维系生命的物质基础，更在生物组织中引发不可逆的恶性连锁反应。”​**​                   | “不可逆”重复窒息   | “污染多不可逆：撕裂生命根基，更在生物组织中引爆死亡链式反应。”              | 删除冗余+冒号递进+动词“撕裂/引爆”升级     |
| ​**​“砷毒理学权威萨特李博士指出，尽管有机杀虫剂已取代砷制剂，烟草植株仍持续吸收旧毒素——烟草种植土壤已饱含铅砷酸盐这类难溶性剧毒残留。”​**​ | 破折号滥用导致因果断裂 | “萨特李博士揭穿残酷现实：有机药虽替砷剂，烟草仍吸毒！根源——土壤早被铅砷酸盐永久毒害。” | 叹号强化矛盾+独立短句“根源...”显性归因    |
| ​**​“被土壤和地表水被农药等化学物质污染，不仅可能引入毒物，更可能掺入致癌物质。”​**​                             | 双“被”字导致语态混乱 | 土壤与地表水被污染后——农药不止可能引入毒物，更可能参入致癌物！              | 删除被动标记+破折号分层+动词“沦陷/暗渡”具象化 |
| ​**​“未来世代恐怕难以宽恕我们对支撑万物生息的自然完整性的草率漠视。”​**​                                   | 定语堆叠阻塞情感传递  | 未来子孙的审判将至——我们，竟漠视自然完整性！这万物生息的根基...            | 破折号切割主客立场+叹号强化罪状+省略号留白    |

四、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。
独立数学公式：对于独立行的数学公式（即$$...$$格式的公式），必须完全按原样保留，不要尝试翻译或修改这些公式。

五、风险代码规避（含有代码和数学公式的混合段落）,特别重要：
特别重要：所有的代码，公式，都不要修改。一个符号也不要改动。
段落中混合的其余非公式，非代码的文字，按照基础要求，写作技巧，断句规则正常翻译。句式若调整，千万不要影响到行内公式，行内公式不要做任何的修改。
特别重要：所有的行内公式，不要修改。一个符号也不要改动。

最后：切记只输出翻译。不要加任何备注信息！

接下来请翻译：

(/prompt)

~~~

# v0.84
新增：约束单独行中的行外公式   强行不翻译，直接抄写。
测试环境：vllm0.9.0  ， qwq32b awq，  温度：0.45   ，上下文1000
测试结果：0.83翻译出来的一对乱的失败的公式，几乎都没问题了。

~~~
(prompt)
v0.84
一、翻译基础要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。
数学公式：一个一个的，一句一句的翻译公式，尽量隔离公式之间的错误。

【特别重要】独立行的LaTeX数学公式处理：
1. 独立行的数学公式是指单独占一行且被$$...$$包围的公式，不需要翻译，直接原样保留
2. 所有$$...$$格式的独立行公式必须保持原样，一字不改地复制到译文中
3. 行内公式（即被单个$符号包围的公式）仍然按照原有方式处理

二、写作技巧（针对非代码和非数学公式段落）：
1、不影响内容和逻辑时，在文章中适当留白，让读者有思考的空间
2、在叙述中分层次表达情感，让情感表达更丰富
3、略微加入幽默元素，让文章更轻松有趣
4、句式要长短结合，徐徐的切换，使得文章不那么生硬。
5、用更简洁的方式传递信息，让句子更加有力
6、删除多余的词语，使表达更加简练
7、把复杂的句子拆开，用短句子表达，更有冲击力
8、用更多动词，让文章更有活力
9、简洁的语言表达复杂的思想，使文章更精炼有力
10、尽量用主动句，增加文章力量
11、全部使用本土句式代替原有句法，不必考虑句式的对应关系。但因果机制要严谨。
12、译文要引发情绪共鸣，增强代入感,但要保证意义等价。全文情感要强烈同时保持一定克制，文笔强烈但不过度夸张。
13、通过双向传递情感，让读者在情感交流中受到感染，保持因果机制要严谨
14、通过句式的长短搭配，使文章读起来有节奏感
15、通过长短句的变化，控制文章的节奏
16、多维度润化译文，增加可读性。但保持不越界。
17、断句要明确，译文中长句子的容忍度为0。断句的优先级是最高的。长句子通通强行拆解为短句的组合，阅读时候要体验出断句的顿挫感，这是简化阅读强度的关键！！

三、详细断句规则（针对非代码和非数学公式段落）：
1、用破折号分层，对比句前置，术语精简。
2、冒号引导递进，拆分定中结构，转折限行化。
3、动宾紧凑衔接，逗号强调主语
4、情景状语前置，冒号列举式，
5、动词强化
6、场景具象化
7、精确量化
8、短句点睛
9、比喻显性化

对比举例第一批：以下是调整后的表格（已去除模型名称，仅保留版本号）：

| 最佳断句案例 (满分)                                      | 最差断句案例 (缺陷)                                  | 核心差异点                      |
| ------------------------------------------------ | -------------------------------------------- | -------------------------- |
| 近年发现的坏死性凋亡——这种受控坏死形式依赖于RIP1/RIP3蛋白。        | 坏死性凋亡被描述为一种依赖RIP1和/或RIP3的受调控坏死形式。      | ✅ 破折号分层 vs ❌ 30字无停顿长句      |
| 与传统坏死不同，坏死性凋亡的典型特征在于分子事件的有序性。              | 分子事件的控制序列是坏死性凋亡的典型特征。                  | ✅ 对比前置 vs ❌ 英文语序直译         |
| 凋亡通过DNA核小体间断裂及半胱天冬酶激活等特征定义。                | 凋亡通过被称为半胱天冬酶（caspases）的细胞内酶的激活来定义。     | ✅ 术语精简 vs ❌ 22字超长插入语       |
| 研究发现：细胞代谢状态不仅决定细胞生存与否，更能精准调控不同死亡模式的启动。    | 细胞代谢对程序性细胞死亡的调控具有深远影响，尤其对死亡类型具有决定性作用。 | ✅ 冒号引导递进 vs ❌ "尤其"逻辑断裂     |
| 深入解析代谢波动如何通过信号转导通路精准调控不同细胞死亡路径。           | 深入解析代谢波动调控不同细胞死亡通路的信号传导级联反应...        | ✅ 拆分定中结构 vs ❌ 16字超长定语      |
| 值得注意的是，代谢异常与细胞死亡通路失调不仅是肿瘤核心特征，同样参与多种人类疾病。 | 由于代谢失调与细胞死亡通路改变还参与了多种人类疾病的发病机制...      | ✅ 转折显性化 vs ❌ "由于"缺失结果句     |
| 精准解析控制死亡模式的分子事件，对选择性干预这些过程至关重要。            | 由于这些不同形式的程序性细胞死亡在正常和病理条件下均参与重要调控...    | ✅ 动宾紧凑衔接 vs ❌ 连词滥用无重点      |
| 本文特别聚焦于代谢与坏死性凋亡之间的关联机制。                   | 本篇综述将聚焦代谢与程序性坏死的关联机制展开探讨。             | ✅ 主动态单句 vs ❌ "聚焦...展开探讨"重复 |
| 癌细胞的代谢特征图谱，势必对恶性转化的各个阶段产生深远影响。             | 这一知识在医学多个领域具有巨大的转化潜力。                 | ✅ 逗号强调主语 vs ❌ 术语孤立无上下文     |
对比举例第二批：

| **原句核心内容**      | **最佳断句案例**                                        | **最差断句案例**                                    | **优化核心**                               |
| --------------- | ------------------------------------------------- | --------------------------------------------- | -------------------------------------- |
| **信号通路与代谢交汇**   | 细胞死亡信号通路与代谢事件的**深度交织**，正推动治疗新策略的诞生。       | 这种细胞死亡信号通路和代谢事件之间的交叉互动正在被研究用于开发新的治疗方法。 (v0.6) | ✅动词动态化("推动") vs ❌被动化("正在被研究")          |
| **代谢波动响应机制**    | 当**葡萄糖供应骤减**时，细胞通过激活AMPK通路**重编程代谢流向**。      | 细胞在葡萄糖供应减少的情况下通过AMPK通路激活进行代谢重编程。        | ✅情景状语前置 vs ❌30字无停顿长句                   |
| **凋亡的生化标志定义**   | 凋亡的判定需满足**三大标志**：染色质凝集、caspase级联激活、凋亡小体形成。 | 凋亡通过DNA核小体间断裂和被称为半胱天冬酶的蛋白酶激活等参数定义。 (v0.6)     | ✅冒号列举式 vs ❌超长定中结构("被称为...的蛋白酶")        |
| **转化医学潜力说明**    | 这些发现**不仅解锁了肿瘤治疗新靶点**，更为代谢性疾病提供干预思路。       | 由于该机制在多种疾病中发挥作用，因此具有转化潜力。               | ✅动词强化("解锁") vs ❌笼统表述("发挥作用")           |
| **坏死性凋亡的病理意义**  | **炎症风暴中**，坏死性凋亡通过释放DAMPs分子**放大组织损伤**。      | 坏死性凋亡在炎症条件下会造成损伤相关分子模式的释放从而导致组织损伤。 (v0.6)     | ✅场景具象化("炎症风暴") vs ❌术语堆砌无断句             |
| **代谢决定死亡类型的机制** | 线粒体代谢产物乙酰-CoA**累积至临界浓度时**，将直接**触发凋亡程序**。    | 当线粒体代谢产物乙酰辅酶A积累到一定水平会诱导细胞凋亡。            | ✅精确量化("临界浓度") vs ❌模糊表述("一定水平")         |
| **分子事件的有序性强调**  | **关键在有序**：坏死性凋亡严格遵循RIP1-RIP3-MLKL磷酸化级联。    | 坏死性凋亡的典型特征是其分子事件序列受到严格调控。               | ✅短句点睛("关键在有序") vs ❌抽象描述("受到严格调控")      |
| **代谢干预的治疗前景**   | 靶向谷氨酰胺代谢**如同切断敌军粮草**，可有效增敏肿瘤细胞死亡。           | 通过抑制谷氨酰胺代谢途径可以增强肿瘤细胞对死亡的敏感性。           | ✅军事比喻显性化 vs ❌机械直译("通过抑制...途径")         |
| **基础研究的临床价值**   | 这些分子机制的解析，**正为"不可成药"靶点带来破局曙光**。             | 对该机制的深入理解可能为难以靶向治疗的靶点提供新思路。             | ✅创新术语("不可成药") + 动态意象("破局曙光") vs ❌模板化表达 |
四、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。
独立数学公式：对于独立行的数学公式（即$$...$$格式的公式），必须完全按原样保留，不要尝试翻译或修改这些公式。

五、风险代码规避（含有代码和数学公式的混合段落）,特别重要：
特别重要：所有的代码，公式，都不要修改。一个符号也不要改动。
段落中混合的其余非公式，非代码的文字，按照基础要求，写作技巧，断句规则正常翻译。句式若调整，千万不要影响到行内公式，行内公式不要做任何的修改。
特别重要：所有的行内公式，不要修改。一个符号也不要改动。

最后：切记只输出翻译。不要加任何备注信息！

接下来请翻译：

(/prompt)

~~~

# V0.83
新增：详细的断句规则，和举例

(prompt)
v0.83
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。

二、写作技巧：
1、不影响内容和逻辑时，在文章中适当留白，让读者有思考的空间
2、在叙述中分层次表达情感，让情感表达更丰富
3、略微加入幽默元素，让文章更轻松有趣
4、句式要长短结合，徐徐的切换，使得文章不那么生硬。
5、用更简洁的方式传递信息，让句子更加有力
6、删除多余的词语，使表达更加简练
7、把复杂的句子拆开，用短句子表达，更有冲击力
8、用更多动词，让文章更有活力
9、简洁的语言表达复杂的思想，使文章更精炼有力
10、尽量用主动句，增加文章力量
11、全部使用本土句式代替原有句法，不必考虑句式的对应关系。但因果机制要严谨。
12、译文要引发情绪共鸣，增强代入感,但要保证意义等价。全文情感要强烈同时保持一定克制，文笔强烈但不过度夸张。
13、通过双向传递情感，让读者在情感交流中受到感染，保持因果机制要严谨
14、通过句式的长短搭配，使文章读起来有节奏感
15、通过长短句的变化，控制文章的节奏
16、多维度润化译文，增加可读性。但保持不越界。
17、断句要明确，译文中长句子的容忍度为0。断句的优先级是最高的。长句子通通强行拆解为短句的组合，阅读时候要体验出断句的顿挫感，这是简化阅读强度的关键！！

三、详细断句规则：
1、用破折号分层，对比句前置，术语精简。
2、冒号引导递进，拆分定中结构，转折限行化。
3、动宾紧凑衔接，逗号强调主语
4、情景状语前置，冒号列举式，
5、动词强化
6、场景具象化
7、精确量化
8、短句点睛
9、比喻显性化

对比举例第一批：以下是调整后的表格（已去除模型名称，仅保留版本号）：

| 最佳断句案例 (满分)                                      | 最差断句案例 (缺陷)                                  | 核心差异点                      |
| ------------------------------------------------ | -------------------------------------------- | -------------------------- |
| 近年发现的坏死性凋亡——这种受控坏死形式依赖于RIP1/RIP3蛋白。（v0.6）        | 坏死性凋亡被描述为一种依赖RIP1和/或RIP3的受调控坏死形式。（v0.6）      | ✅ 破折号分层 vs ❌ 30字无停顿长句      |
| 与传统坏死不同，坏死性凋亡的典型特征在于分子事件的有序性。（v0.6）              | 分子事件的控制序列是坏死性凋亡的典型特征。（v0.6）                  | ✅ 对比前置 vs ❌ 英文语序直译         |
| 凋亡通过DNA核小体间断裂及半胱天冬酶激活等特征定义。（v0.5）                | 凋亡通过被称为半胱天冬酶（caspases）的细胞内酶的激活来定义。（v0.6）     | ✅ 术语精简 vs ❌ 22字超长插入语       |
| 研究发现：细胞代谢状态不仅决定细胞生存与否，更能精准调控不同死亡模式的启动。（v0.81）    | 细胞代谢对程序性细胞死亡的调控具有深远影响，尤其对死亡类型具有决定性作用。（v0.82） | ✅ 冒号引导递进 vs ❌ "尤其"逻辑断裂     |
| 深入解析代谢波动如何通过信号转导通路精准调控不同细胞死亡路径。（v0.81）           | 深入解析代谢波动调控不同细胞死亡通路的信号传导级联反应...（v0.82）        | ✅ 拆分定中结构 vs ❌ 16字超长定语      |
| 值得注意的是，代谢异常与细胞死亡通路失调不仅是肿瘤核心特征，同样参与多种人类疾病。（v0.81） | 由于代谢失调与细胞死亡通路改变还参与了多种人类疾病的发病机制...（v0.6）      | ✅ 转折显性化 vs ❌ "由于"缺失结果句     |
| 精准解析控制死亡模式的分子事件，对选择性干预这些过程至关重要。（v0.6）            | 由于这些不同形式的程序性细胞死亡在正常和病理条件下均参与重要调控...（v0.5）    | ✅ 动宾紧凑衔接 vs ❌ 连词滥用无重点      |
| 本文特别聚焦于代谢与坏死性凋亡之间的关联机制。（v0.81）                   | 本篇综述将聚焦代谢与程序性坏死的关联机制展开探讨。（v0.81）             | ✅ 主动态单句 vs ❌ "聚焦...展开探讨"重复 |
| 癌细胞的代谢特征图谱，势必对恶性转化的各个阶段产生深远影响。（v0.5）             | 这一知识在医学多个领域具有巨大的转化潜力。（v0.82）                 | ✅ 逗号强调主语 vs ❌ 术语孤立无上下文     |
对比举例第二批：

| **原句核心内容**      | **最佳断句案例**                                        | **最差断句案例**                                    | **优化核心**                               |
| --------------- | ------------------------------------------------- | --------------------------------------------- | -------------------------------------- |
| **信号通路与代谢交汇**   | 细胞死亡信号通路与代谢事件的**深度交织**，正推动治疗新策略的诞生。 (v0.81)       | 这种细胞死亡信号通路和代谢事件之间的交叉互动正在被研究用于开发新的治疗方法。 (v0.6) | ✅动词动态化("推动") vs ❌被动化("正在被研究")          |
| **代谢波动响应机制**    | 当**葡萄糖供应骤减**时，细胞通过激活AMPK通路**重编程代谢流向**。 (v0.6)     | 细胞在葡萄糖供应减少的情况下通过AMPK通路激活进行代谢重编程。 (v0.6)       | ✅情景状语前置 vs ❌30字无停顿长句                   |
| **凋亡的生化标志定义**   | 凋亡的判定需满足**三大标志**：染色质凝集、caspase级联激活、凋亡小体形成。 (v0.6) | 凋亡通过DNA核小体间断裂和被称为半胱天冬酶的蛋白酶激活等参数定义。 (v0.6)     | ✅冒号列举式 vs ❌超长定中结构("被称为...的蛋白酶")        |
| **转化医学潜力说明**    | 这些发现**不仅解锁了肿瘤治疗新靶点**，更为代谢性疾病提供干预思路。 (v0.81)       | 由于该机制在多种疾病中发挥作用，因此具有转化潜力。 (v0.82)             | ✅动词强化("解锁") vs ❌笼统表述("发挥作用")           |
| **坏死性凋亡的病理意义**  | **炎症风暴中**，坏死性凋亡通过释放DAMPs分子**放大组织损伤**。 (v0.81)     | 坏死性凋亡在炎症条件下会造成损伤相关分子模式的释放从而导致组织损伤。 (v0.6)     | ✅场景具象化("炎症风暴") vs ❌术语堆砌无断句             |
| **代谢决定死亡类型的机制** | 线粒体代谢产物乙酰-CoA**累积至临界浓度时**，将直接**触发凋亡程序**。 (v0.6)   | 当线粒体代谢产物乙酰辅酶A积累到一定水平会诱导细胞凋亡。 (v0.5)           | ✅精确量化("临界浓度") vs ❌模糊表述("一定水平")         |
| **分子事件的有序性强调**  | **关键在有序**：坏死性凋亡严格遵循RIP1-RIP3-MLKL磷酸化级联。 (v0.81)   | 坏死性凋亡的典型特征是其分子事件序列受到严格调控。 (v0.6)              | ✅短句点睛("关键在有序") vs ❌抽象描述("受到严格调控")      |
| **代谢干预的治疗前景**   | 靶向谷氨酰胺代谢**如同切断敌军粮草**，可有效增敏肿瘤细胞死亡。 (v0.8)          | 通过抑制谷氨酰胺代谢途径可以增强肿瘤细胞对死亡的敏感性。 (v0.82)          | ✅军事比喻显性化 vs ❌机械直译("通过抑制...途径")         |
| **基础研究的临床价值**   | 这些分子机制的解析，**正为"不可成药"靶点带来破局曙光**。 (v0.6)            | 对该机制的深入理解可能为难以靶向治疗的靶点提供新思路。 (v0.5)            | ✅创新术语("不可成药") + 动态意象("破局曙光") vs ❌模板化表达 |
四、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。

五、风险代码规避
删除不重要数学公式：遇到句子或者数学公式块中含有\mathbf 以及\mathrm以及 \scriptstyle等等这种不重要的数学公式（涵盖如字体，大小，间距，颜色等，格式控制，排版优化），讲这些标签全部删除。整句或者整块转化为纯文本以提高兼容性。
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。
代码块错误：尽量少用行内代码块  比如 'define'中尽量去掉单引号。段落的代码块要封闭，完整。
多行代码块检查：正文千万不要被放入多行代码块内。极度谨慎的选择多行代码块的作用范围，只能包含程序源码，不能包含正文文本，且每次翻译出现了代码块必须开始和结束标签完整，保持封闭性。否则，我的翻译后合并的书籍由于确实结束标案全部编程代码块的文本了。

六、内容修正
代码修复：有些是ocr后的代码，很乱，有错字，必须主动去修复，并制作成规范的代码格式。
文字修复：有些是ocr后的文本，所以正文中经常出现的错字，必须根据上下文进行修复。
修复的准确性：专业名词不要去合并翻译，要一一对应的翻译和修正。

最后：切记只输出翻译。不要加任何备注信息！

接下来请翻译：

(/prompt)
# v0.82
修改：加强断句

(prompt)
v0.81
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。

二、写作技巧：
1、不影响内容和逻辑时，在文章中适当留白，让读者有思考的空间
2、在叙述中分层次表达情感，让情感表达更丰富
3、略微加入幽默元素，让文章更轻松有趣
4、句式要长短结合，徐徐的切换，使得文章不那么生硬。
5、用更简洁的方式传递信息，让句子更加有力
6、删除多余的词语，使表达更加简练
7、把复杂的句子拆开，用短句子表达，更有冲击力
8、用更多动词，让文章更有活力
9、简洁的语言表达复杂的思想，使文章更精炼有力
10、尽量用主动句，增加文章力量
11、全部使用本土句式代替原有句法，不必考虑句式的对应关系。但因果机制要严谨。
12、译文要引发情绪共鸣，增强代入感,但要保证意义等价。全文情感要强烈同时保持一定克制，文笔强烈但不过度夸张。
13、通过双向传递情感，让读者在情感交流中受到感染，保持因果机制要严谨
14、通过句式的长短搭配，使文章读起来有节奏感
15、通过长短句的变化，控制文章的节奏
16、多维度润化译文，增加可读性。但保持不越界。
17、断句要明确，译文中长句子的容忍度为0。断句的优先级是最高的。长句子通通强行拆解为短句的组合，阅读时候要体验出断句的顿挫感，这是简化阅读强度的关键！！

三、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。

四、风险代码规避
删除不重要数学公式：遇到句子或者数学公式块中含有\mathbf 以及\mathrm以及 \scriptstyle等等这种不重要的数学公式（涵盖如字体，大小，间距，颜色等，格式控制，排版优化），讲这些标签全部删除。整句或者整块转化为纯文本以提高兼容性。
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。
代码块错误：尽量少用行内代码块  比如 'define'中尽量去掉单引号。段落的代码块要封闭，完整。
多行代码块检查：正文千万不要被放入多行代码块内。极度谨慎的选择多行代码块的作用范围，只能包含程序源码，不能包含正文文本，且每次翻译出现了代码块必须开始和结束标签完整，保持封闭性。否则，我的翻译后合并的书籍由于确实结束标案全部编程代码块的文本了。

五、内容修正
代码修复：有些是ocr后的代码，很乱，有错字，必须主动去修复，并制作成规范的代码格式。
文字修复：有些是ocr后的文本，所以正文中经常出现的错字，必须根据上下文进行修复。
修复的准确性：专业名词不要去合并翻译，要一一对应的翻译和修正。

最后：切记只输出翻译。不要加任何备注信息！

接下来请翻译：

(/prompt)
# v0.81
增加：精准控制句式，润化，风格。

(prompt)
v0.81
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。

二、写作技巧：
1、不影响内容和逻辑时，在文章中适当留白，让读者有思考的空间
2、在叙述中分层次表达情感，让情感表达更丰富
3、略微加入幽默元素，让文章更轻松有趣
4、句式要长短结合，徐徐的切换，使得文章不那么生硬。
5、用更简洁的方式传递信息，让句子更加有力
6、删除多余的词语，使表达更加简练
7、把复杂的句子拆开，用短句子表达，更有冲击力
8、用更多动词，让文章更有活力
9、简洁的语言表达复杂的思想，使文章更精炼有力
10、尽量用主动句，增加文章力量
11、全部使用本土句式代替原有句法，不必考虑句式的对应关系。但因果机制要严谨。
12、译文要引发情绪共鸣，增强代入感,但要保证意义等价。全文情感要强烈同时保持一定克制，文笔强烈但不过度夸张。
13、通过双向传递情感，让读者在情感交流中受到感染，保持因果机制要严谨
14、通过句式的长短搭配，使文章读起来有节奏感
15、通过长短句的变化，控制文章的节奏
16、多维度润化译文，增加可读性。但保持不越界。

三、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。

四、风险代码规避
删除不重要数学公式：遇到句子或者数学公式块中含有\mathbf 以及\mathrm以及 \scriptstyle等等这种不重要的数学公式（涵盖如字体，大小，间距，颜色等，格式控制，排版优化），讲这些标签全部删除。整句或者整块转化为纯文本以提高兼容性。
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。
代码块错误：尽量少用行内代码块  比如 'define'中尽量去掉单引号。段落的代码块要封闭，完整。
多行代码块检查：正文千万不要被放入多行代码块内。极度谨慎的选择多行代码块的作用范围，只能包含程序源码，不能包含正文文本，且每次翻译出现了代码块必须开始和结束标签完整，保持封闭性。否则，我的翻译后合并的书籍由于确实结束标案全部编程代码块的文本了。

五、内容修正
代码修复：有些是ocr后的代码，很乱，有错字，必须主动去修复，并制作成规范的代码格式。
文字修复：有些是ocr后的文本，所以正文中经常出现的错字，必须根据上下文进行修复。
修复的准确性：专业名词不要去合并翻译，要一一对应的翻译和修正。

最后：切记只输出翻译。不要加任何备注信息！

接下来请翻译：

(/prompt)

# 提示词 v0.7
增加：强调只输出翻译。
修复：数学公式优化
修复：控制代码区域标签输出。
新增：提示词标签化

(prompt)
v0.7
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。
智能断句：太长的不利于阅读的句子要根据语义转换为中文环境中的若干短句组合。断句要完全匹配中文阅读习惯，不需要和外文句式对应。
输出最终翻译：输出的时候只输出最终翻译，不要有任何解释或者说明，否则我最终翻译的书籍中会夹杂太多垃圾信息。

二、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。

三、风险代码规避
删除不重要数学公式：遇到句子或者数学公式块中含有\mathbf 以及\mathrm以及 \scriptstyle等等这种不重要的数学公式（涵盖如字体，大小，间距，颜色等，格式控制，排版优化），讲这些标签全部删除。整句或者整块转化为纯文本以提高兼容性。
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。
代码块错误：尽量少用行内代码块  比如 'define'中尽量去掉单引号。段落的代码块要封闭，完整。
多行代码块检查：正文千万不要被放入多行代码块内。极度谨慎的选择多行代码块的作用范围，只能包含程序源码，不能包含正文文本，且每次翻译出现了代码块必须开始和结束标签完整，保持封闭性。否则，我的翻译后合并的书籍由于确实结束标案全部编程代码块的文本了。

四、内容修正
代码修复：有些是ocr后的代码，很乱，有错字，必须主动去修复，并制作成规范的代码格式。
文字修复：有些是ocr后的文本，所以正文中经常出现的错字，必须根据上下文进行修复。
修复的准确性：专业名词不要去合并翻译，要一一对应的翻译和修正。

接下来请翻译：

(/prompt)