import os
import re

def process_root_directories():
    """处理当前根目录下的所有文件夹（无论是否包含子目录）"""
    print("Processing root directories...")
    changed = False
    for entry in os.listdir():
        entry_path = os.path.join(os.getcwd(), entry)
        if os.path.isdir(entry_path):  # 仅处理目录
            # 匹配最后一个 .pdf 后的内容（不区分大小写）
            new_name = re.sub(r'(\.pdf).*', r'\1', entry, flags=re.IGNORECASE)
            if new_name != entry:
                try:
                    os.rename(entry, new_name)
                    print(f"Renamed: {entry} → {new_name}")
                    changed = True
                except OSError as e:
                    print(f"Error renaming {entry}: {e}")
            else:
                print(f"No change needed: {entry}")
        else:
            print(f"Skipping non-directory: {entry}")
    if not changed:
        print("No directories needed modification.")

def main():
    while True:
        process_root_directories()
        print("\nOptions:")
        print("1. Process again")
        print("2. Exit")
        choice = input("Enter your choice (1/2): ").strip()
        if choice == '2':
            print("Exiting program.")
            break
        elif choice != '1':
            print("Invalid choice. Exiting.")
            break

if __name__ == "__main__":
    main()