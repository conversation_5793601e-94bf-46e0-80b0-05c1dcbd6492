import os
import re
import tkinter as tk
from tkinter import messagebox

def count_units(text):
    """计算文本的字符单位（汉字算2，其他字符算1）"""
    count = 0
    for c in text:
        if '\u4e00' <= c <= '\u9fff':  # 判断是否为中文字符
            count += 2
        else:
            count += 1
    return count

def process_line(line):
    """处理单行文本，符合条件的标题行删除标记"""
    # 新增标题降级逻辑
    line = re.sub(r'^(#{1,2})\s', r'### ', line)
    
    header_pattern = re.compile(r'^(#+)\s*(.*)$')
    demoted = False
    match = header_pattern.match(line)
    if match:
        content = match.group(2)
        if count_units(content) > 120:
            demoted = True
            return (content + '\n', demoted)  # 保留换行符
    return (line, demoted)

def get_unique_filename(base_path, mode):
    """根据模式生成文件名"""
    if mode == 'sequence':
        index = 1
        new_path = f"{base_path}-md优化.md"
        while os.path.exists(new_path):
            new_path = f"{base_path}-md优化-{index}.md"
            index += 1
        return new_path
    elif mode == 'overwrite':
        return f"{base_path}-md优化-0000.md"

def process_file(file_path, mode):
    """处理单个文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    new_lines = []
    changed_lines = []
    demote_count = 0
    sentence_converted = 0
    
    for line in lines:
        new_line, is_demoted = process_line(line)
        new_lines.append(new_line)
        if is_demoted:
            demote_count += 1
            if line.strip().startswith('#'):
                sentence_converted += 1
        if new_line != line:
            changed_lines.append((line.strip(), new_line.strip()))
    
    modified_count = len(changed_lines)
    filename = os.path.basename(file_path)
    print(f"文件 {filename} 处理完成，格式调整{modified_count}处，标题降级{demote_count}次，其中{sentence_converted}个标题转为正文")
    
    if modified_count > 0:
        base_name = os.path.splitext(file_path)[0]
        output_path = get_unique_filename(base_name, mode)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        print(f"生成新文件：{os.path.basename(output_path)}\n")

class App:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title('MD文件优化工具')
        
        btn_frame = tk.Frame(self.window)
        btn_frame.pack(pady=20)

        tk.Button(btn_frame, text="生成新序号文件", command=lambda: self.run_processing('sequence')).pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="覆盖固定文件", command=lambda: self.run_processing('overwrite')).pack(side=tk.LEFT, padx=10)

    def run_processing(self, mode):
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            processed_dirs = 0
            for entry in os.scandir(current_dir):
                if entry.is_dir(follow_symlinks=False):
                    processed_dirs += 1
                    print(f"\n正在扫描目录：{entry.name}")
                    file_count = 0
                    
                    # 精确处理一级子目录的文件
                    for item in os.scandir(entry.path):
                        if item.is_file() and \
                           not item.name.endswith('md优化.md') and \
                           item.name != 'full.md' and \
                           not re.search(r'md优化-\d+\.md$', item.name) and \
                           item.name.endswith('.md'):
                            file_count += 1
                            print(f"正在处理第 {file_count} 个文件：{item.name}")
                            process_file(item.path, mode)
                    
                    print(f"目录 {entry.name} 处理完成，共处理 {file_count} 个文件")
                    
            if processed_dirs == 0:
                print("\n警告：未找到任何包含翻译文件的子目录！")
                print("请确认：")
                print("1. 是否在脚本所在目录创建了子目录")
                print("2. 子目录中是否包含符合命名规范（*.pdf.md）的翻译文件")
        
        except Exception as e:
            print(f"\n处理过程中发生错误：{str(e)}")
            messagebox.showinfo("完成", "文件处理完成！")
        except Exception as e:
            messagebox.showerror("错误", str(e))

if __name__ == '__main__':
    App().window.mainloop()
    main()

input("按任意键退出...")